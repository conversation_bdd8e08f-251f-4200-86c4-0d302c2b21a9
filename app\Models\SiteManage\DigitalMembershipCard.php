<?php

namespace App\Models\SiteManage;

use App\Models\SiteManage\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class DigitalMembershipCard
 *
 * This class represents the DigitalMembershipCard model, which is used to manage digital membership cards.
 *
 * @package App\Models\SiteManage
 */
class DigitalMembershipCard extends BaseModel
{
    use HasFactory;

    protected $table = 'digital_membership_cards';

    protected $fillable = [
        'id',
        'status',
        'default',
        'title',
        'image',
        'position'
    ];
}