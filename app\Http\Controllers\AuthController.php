<?php

namespace App\Http\Controllers;

use App\Shared\Constant;
use Illuminate\Http\Request;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use Validator;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{

    /**
     * Login to website.
     * @param Request $request
     * @param JWTAuth $jwtAuth
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request, JWTAuth $jwtAuth)
    {
        $data = [
            'email' => $request->email,
            'password' => $request->password,
            'status' => Constant::ADMIN_STATUS_ACTIVE
        ];
        $this->validateLoginData($data);

        $auth = $jwtAuth->attempt($data);

        if (!$token = $auth) {
            return response()->json([
                'success' => false,
                'message' => Constant::AUTHENTICATION_FAILED,
                'error' => [
                    'message' => Constant::INCORECT_EMAIL_OR_PASSWORD
                ]
            ]);
        }

        $admin = $jwtAuth->user();
        $profile = $admin->with(['adminWebsites.website', 'adminRole.role'])->find($admin->id);

        return response()->json([
            'success' => true,
            'profile' => $profile,
            'access_token' => $token
        ]);
    }
    /**
     * Validate login data.
     * @param array $data
     * @return \Illuminate\Http\JsonResponse|null
     */
    private function validateLoginData(array $data)
    {
        Log::info("AuthController - Method: validateLoginData");
        $validator = Validator::make($data, [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ]);
        }

    }
}
