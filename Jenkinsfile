pipeline {
    agent any
    stages {
        stage('Checkout') {
			steps {
				checkout scm
			}
		}
        stage('Build & PHP UnitTest') {
            steps {
                sh "cp .env.example .env"
                sh "composer install --no-dev --optimize-autoloader"
                sh "php artisan config:clear"
                sh "php artisan cache:clear"
                sh "php artisan optimize"
            }
        }
        stage('File Permission') {
            steps {
                sh "sudo chmod -R 777 /opt/ttech"
                sh "sudo chmod -R 777 /var/lib/jenkins/workspace/dev-ttech-admin-api"
            }
        }
    }
}