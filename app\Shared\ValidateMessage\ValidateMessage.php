<?php

namespace App\Shared\ValidateMessage;

final class ValidateMessage
{

    public const VALIDATE_SUCCESS = 'Validate success';

    public const VALIDATE_FAIL = 'Validate fail';
    public const USERNAME_REQUIRED = 'Username is required';

    public const USERNAME_MAX_LENGTH = 'Username length from 1 to 50 characters';

    public const USERNAME_MIN_LENGTH = 'Username length from 1 to 50 characters';

    public const USERNAME_UNIQUE = 'unique';

    public const GENDER_REQUIRED = 'Gender is required';

    public const TOKEN_REQUIRED = 'Token is required';

    public const TOKEN_EXPIRE = 'Token is expired';

    public const TOKEN_NOT_EXiST = 'Token is not exist';

    public const EMAIL_REQUIRED = 'required';

    public const EMAIL_UNIQUE = 'unique';

    public const MEMBER_NAME_UNIQUE = 'unique';

    public const EMAIL_FORMAT = 'format';

    public const DATE_OF_BIRTH_FORMAT = 'Date of birth format is Y-m-d';

    public const DATE_OF_BIRTH_REQUIRED = 'Date of birth is required';

    public const PASSWORD_REQUIRED = 'Password is required';

    public const PASSWORD_MAX_LENGTH = 'Password length from 6 to 30 characters';

    public const PASSWORD_MIN_LENGTH = 'Password length from 6 to 30 characters';

    public const PASSWORD_FORMAT = 'Password format is invalid';

    public const CONFIRM_PASSWORD_REQUIRED = 'Confirm password is required';

    public const CONFIRM_PASSWORD_NOT_SAME = 'Confirm password not same';

    public const NEW_PASSWORD_REQUIRED = 'new password is required';

    public const NEW_PASSWORD_MIN = 'new password length from 6 to 30 characters';

    public const NEW_PASSWORD_MAX = 'new password length from 6 to 30 characters';

    public const TOKEN_IS_INVALID = 'token is invalid';

    public const RESET_PASSWORD_FAILED = 'RESET_PASSWORD_FAILED';

    public const CREATE_MEMBER_FAILED = 'CREATE_MEMBER_FAILED';

    public const DELETE_MEMBER_FAILED = 'DELETE_MEMBER_FAILED';

    public const MEMBER_NOT_FOUND = 'MEMBER_NOT_FOUND';

    public const UPDATE_MEMBER_FAILED = 'UPDATE_MEMBER_FAILED';

    public const BANNER_TRASITIONURL_REQUIRED = 'Transition_url is required';

    public const BANNER_STARTAT_REQUIRED = 'Start_at is required';

    public const BANNER_ENDAT_REQUIRED = 'End_at is required';

    public const BANNER_IMAGE_REQUIRED = 'Image is required';

    public const BANNER_IMAGE_MIMES = 'Image is not in the correct format';

    public const BANNER_IMAGE_SIZE = 'Image must be smaller than 2048';

    public const BANNER_POSITION_REQUIRED = 'Position is required';

    public const BANNER_POSITION_MIN = 'Position from 1 to 10';

    public const BANNER_POSITION_MAX = 'Position from 1 to 10';

    public const BANNER_STATUS_REQUIRED = 'Status is required';

    public const ROLE_ID_REQUIRED = 'required';

    public const SITE_ID_REQUIRED = 'required';

    public const CATEGORY_NAME_REQUIRED = 'Category is required';

    public const CATEGORY_NAME_MIN = 'Category name min length is 1';

    public const CATEGORY_NAME_MAX = 'Category name max length is 100';

    public const CATEGORY_NAME_UNIQUE = 'Category name have been taken';

    public const DELETE_CATEGORY_FAILED = 'DELETE_CATEGORY_FAILED';

    public const UPDATE_CATEGORY_FAILED = 'UPDATE_CATEGORY_FAILED';

    public const CREATE_CATEGORY_FAILED = 'CREATE_CATEGORY_FAILED';

    public const CREATE_FAQ_CATEGORY_FAILED = 'CREATE_FAQ_CATEGORY_FAILED';

    public const DELETE_FAQ_CATEGORY_FAILED = 'DELETE_FAQ_CATEGORY_FAILED';

    public const FAQ_CATEGORY_FAILED = 'FAQ_CATEGORY__NOT_FOUND';

    public const FAQ_CATEGORY_NAME_REQUIRED = 'Faq_Category is required';

    public const FAQ_CATEGORY_NAME_MIN = 'Faq_Category name min length is 1';

    public const FAQ_CATEGORY_NAME_MAX = 'Faq_Category name max length is 300';

    public const FAQ_CATEGORY_NAME_UNIQUE = 'Faq_Category name have been taken';

    public const UPDATE_FAQ_CATEGORY_FAILED = 'UPDATE_FAQ_CATEGORY_FAILED';

    public const HAS_ONLY_ONE_DEFAULT_ON = 'HAS_ONLY_ONE_DEFAULT_ON';
}
