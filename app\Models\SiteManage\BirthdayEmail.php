<?php

namespace App\Models\SiteManage;

use App\Models\SiteManage\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * The BirthdayEmail model represents the birthday email configuration.
 *
 * This model extends the BaseModel and uses the HasFactory trait.
 * It is associated with the 'birthday_email' table in the database.
 */
class BirthdayEmail extends BaseModel
{
    use HasFactory;

    protected $table = 'birthday_email';

    protected $fillable = [
        'id',
        'status',
        'delivery_time',
        'title',
        'content'
    ];
}