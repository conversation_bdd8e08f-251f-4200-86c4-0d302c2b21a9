<?php

namespace App\Http\Middleware;

use App\Shared\Constant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleDatabaseConnectionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {   
        $adminWebsites = auth()->user()->adminWebsites;
        $dataHeaders = getallheaders();
        if (!isset($dataHeaders[Constant::KEY_SITE])) {
            return response()->json([], 403);
        }
        if (isset(Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]])) {
            $isAdminWebsite = false;
            foreach ($adminWebsites as $adminWebsite) {
                if ($adminWebsite->website->alias == $dataHeaders[Constant::KEY_SITE]) {
                    $isAdminWebsite = true;
                }
            }
            if ($isAdminWebsite) {
                $connection = Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
                $request->merge([Constant::SITE_CONNECTION => $connection]);
                return $next($request);
            }

        }

        return response()->json([], 403);
    }
}
