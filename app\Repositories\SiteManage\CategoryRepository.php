<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\Category;
use App\Models\SiteManage\Events;
use App\Repositories\BaseRepository;
use Illuminate\Support\Facades\DB;

/**
 * News Repository.
 */
class CategoryRepository
{
    /**
     *
     */
    protected $table = 'categories';
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'created_at';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
    */
    const PAGING_SIZE = 10;

    /**
     * Get list category.
     * @param array $param
     * @return
     */
    public function list($param)
    {
        $q = Category::query();
        $total = $q->count();
        if (isset($param['category_name']) || isset($param['id'])) {
            
            $name = $param['category_name'];
            $id = $param['id'];

            $q->when($param['category_name'], function ($query) use ($name) {
                $query->where('category_name', 'LIKE', '%' . $name . '%');
            });
            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'LIKE', '%' . $id . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort']; //ASC,DESC
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0] ?? self::DEFAULT_SORT_COLUMN, $order[1] ?? self::DEFAULT_SORT_TYPE);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }

        if (isset($param['size']) && (int)$param['size']) {
            $news = $q->paginate((int)$param['size']);
        } else if ($param['size'] != 'all') {
            $news = $q->paginate(self::PAGING_SIZE);
        } else {
            $news = $q->paginate($total);
        }

        return $news;
    }

    /**
     * Get category detail.
     * @param integer $id
     * @return array
     */
    public function detail($id)
    {
        $id = (int)$id;
        $category = Category::where('id', $id)->first();
        return [
            'success' => $category ? true : false,
            'data' => $category,
            'message' => !$category ? 'ID is invalid' : ''
        ];
    }

    /**
     * Create category
     * @param array $data
     * @return
     */
    public function create(array $data)
    {
        /**
         * Process data before create
         */
        $data = Category::getDataFromRequest($data);
        $created = Category::create($data);
        if (!$created) {
            return false;
        }
        return true;
    }

    /**
     * @param array $data
     * @param integer $id
     * @return bool
     */
    public function update(array $data, $id)
    {
        /**
         * Process data before update
         */
        $category = Category::find($id);
        if (!isset($category)) {
            return false;
        }
        $category->category_name = $data['category_name'];
        $category->save();
        if ($category) {
            return true;
        }
        return false;
    }

    /**
     * @param integer $id
     * @return bool
     */
    public function delete($id)
    {
        $category = Category::find($id);
        if (!isset($category)) {
            return false;
        }
        $category->delete();
        $events = Events::where('category_id', $id)->get();
        if (!isset($events)) {
            return true;
        } else {
            foreach ($events as $event) {
                $event->category_id = null;
                $event->save();
            }
            return true;
        }
    }
}
