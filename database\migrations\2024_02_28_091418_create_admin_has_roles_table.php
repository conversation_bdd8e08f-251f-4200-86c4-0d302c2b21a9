<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_has_roles', function (Blueprint $table) {
            $table->id()->startingValue(3000000000);
            $table->bigInteger('admin_id');

            // foreign key
            $table->foreign('admin_id')->references('id')->on('admins');

            // foreign key
            $table->bigInteger('admin_role_id');
            $table->foreign('admin_role_id')->references('id')->on('admin_roles');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_has_roles');
    }
};
