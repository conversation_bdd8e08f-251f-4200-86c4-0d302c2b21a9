<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\Banners;
use App\Models\SiteManage\BaseModel;
use Illuminate\Support\Facades\DB;

class BannerRepository
{
    /**
     *
     */
    protected $table = 'banners';
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'position';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'asc';
    /*
     * PAGING_SIZE
    */
    const PAGING_SIZE = 10;
    /**
     * BANNER_STATUS_PUBLIC
     */
    const BANNER_STATUS_PUBLIC = 'public';

    /**
     * Get list news.
     * @param array $param
     * @return array
     */

    public function list($param)
    {
        $q = Banners::query();
        $size = 0;
        if (isset($param['searchTransitionUrl']) || isset($param['id']) || isset($param['status'])) {
            $transitionURL = $param['searchTransitionUrl'];
            $id = $param['id'];
            $status = $param['status'];

            $q->when($param['searchTransitionUrl'], function ($query) use ($transitionURL) {
                $query->where('transition_url', 'LIKE', '%' . $transitionURL . '%');
            });

            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'LIKE', '%' . $id . '%');
            });

            $q->when($param['status'], function ($query) use ($status) {
                $query->where('status', '=', $status);
            });
        }
        if (isset($param['sort'])) {
            $sort = $param['sort'];
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0] ?? self::DEFAULT_SORT_COLUMN, $order[1] ?? self::DEFAULT_SORT_TYPE);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
            $q->orderBy('created_at', 'desc');
        }

        if (isset($param['size']) && (int)$param['size']) {
            $banners = $q->paginate((int)$param['size']);
            $size = (int)$param['size'];
        } else if ($param['size'] != 'all') {
            $banners = $q->paginate(self::PAGING_SIZE);
            $size = self::PAGING_SIZE;
        } else {
            $banners = $q->paginate($q->count());
            $size = $q->count();
        }
        return [
            "data" => $banners->items(),
            "per_page" => count($banners->items()),
            "total" => $banners->total(),
            "current_page" => $banners->currentPage(),
            "size" => $size
        ];
    }

    /**
     * Get banner detail.
     * @param mixed $id
     * @return mixed
     */
    public function detail($id)
    {
        $banner = Banners::query()->find($id);
        return $banner;
    }

    /**
     * Create banner.
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        $data['created_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $data['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();

        $created = true;
        try {
            BaseModel::beginTransaction();
            $created = Banners::query()->create($data);
            if ($created) {
                $bannerPosition = Banners::select('id', 'position', 'updated_at')->orderBy('position', 'asc')->orderBy('created_at', 'desc')->get();
                $idx = 1;
                foreach($bannerPosition as $banner) {
                    $banner->position = $idx;
                    // $banner['updated_at']= \Carbon\Carbon::now()->toDateTimeString();
                    $banner->save();
                    $idx++;
                }
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $created = false;
            BaseModel::rollBack();
        }
        return $created;

    }

    /**
     * count banners have status is public 
     */
    public function getCountPublicBanner()
    {
        $banners = Banners::query()->where('status', self::BANNER_STATUS_PUBLIC);

        return $banners->count();
    }

    /**
     * Update banner.
     * @param array $data
     * @param int $id
     * @return mixed
     */
    public function update(array $data, int $id)
    {
        $data['created_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $data['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
        return Banners::where('id', (int)$id)->update($data);
    }

    /**
     * Update list banner position
     * @param array $data
     * @return boolean
     */
    public function updatePosition($data)
    {
        /**
         * Process data before update
         */
        $total = Banners::count();
        $newData = array_fill(1, $total, null);
        foreach($data as $e) {
            $position = ($e['position']);
            while ($newData[$position] != null) {
                if ($newData[$position]['id'] < $e['id']) {
                    $id = $newData[$position]['id'];
                    $newData[$position]['id'] = $e['id'];
                    $e['id'] = $id;
                }
                $position++;
            }
            $newData[$position] = [
                'id'=>$e['id'],
                'position' => $position
            ];
        }

        $idsChange = collect($data)->pluck('id');
        $bannerPosition = Banners::select('id', 'position')->whereNotIn('id', $idsChange)->orderBy('position', 'asc')->get();
        $idx = 1;
        foreach($bannerPosition as $position) {
            while ($newData[$idx] != null) {
                $idx++;
            }
            $newData[$idx] = [
                'id'=>$position->id,
                'position' => $idx
            ];
            $idx++;
        }

        $updated = true;
        DB::transaction(function () use ($newData, $updated) {
            for($i = 1; $i <= count($newData); $i++) {
                $element = $newData[$i];
                $element['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
                $update = Banners::where('id', $element['id'])->update($element);
                if (!$update) {
                    $updated = false;
                    DB::rollBack();
                }
            }
            return compact('updated');
        });

        return $updated;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function delete($id)
    {
        $id = (int)$id;
        return Banners::where('id', $id)->delete();
    }
}
