<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\Setting;

/**
 * The SettingRepository class is responsible for retrieving and updating settings.
 */
class SettingRepository
{
    /**
     * Get all settings.
     *
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function getAll()
    {
        return Setting::all();
    }

    /**
     * Update a setting.
     *
     * @param string $key The name of the setting.
     * @param mixed $value The new value for the setting.
     * @return void
     */
    public function update($key, $value)
    {
        Setting::where('name', $key)->update(['status' => $value]);
    }
}