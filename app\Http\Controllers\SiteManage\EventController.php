<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\Events;
use App\Services\SiteManage\EventService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * Events Controller.
 *
 */
class EventController extends Controller
{
    /**
     * @var Events::class
     */
    protected $objectModel = Events::class;
    /**
     * @var EventService
     */
    private $eventService;

    /**
     * @param EventService $eventService
     */
    public function __construct(EventService $eventService)
    {
        $this->eventService = $eventService;
    }

    /**
     * Get list event.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'title' => request('titleFilter'),
                'status' => request('statusFilter'),
                'eventType' => request('eventTypeFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];
            return response()->json($this->eventService->list($param));
        }

        return response()->json([], 403);
    }

    /**
     * Get event detail.
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            return response()->json($this->eventService->detail($id));
        }

        return response()->json([], 403);
    }

    /**
     * Create event.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            return response()->json($this->eventService->create($request->all()));
        }

        return response()->json([], 403);
    }

    /**
     * Update event.
     * @param Request $request
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }

            $param = $request->all();

            return response()->json($this->eventService->update($param, $id));
        }

        return response()->json([], 403);
    }

    /**
     * Update event position.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePosition(Request $request)
    {
        if ($this->hasPermission('update')) {
            return response()->json($this->eventService->updatePosition($request->data));
        }

        return response()->json([], 403);
    }

    /**
     * Upload Image.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        Log::info("uploadImage :" . request('type'));
        if ($this->hasPermission('create')) {
            return response()->json($this->eventService->uploadImage($request->upload, request('type')));
        }

        return response()->json([], 403);
    }

    /**
     * Delete event.
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            return response()->json($this->eventService->delete($id));
        }

        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return array
     */
    private function arrayRules(Request $request)
    {
        // Check if status is draft
        $isDraft = $request->input('status') === 'draft';

        return [
            'title' => $isDraft ? 'max:100' : 'required|max:100',
            'status' => 'required',
            'event_type' => $isDraft ? '' : 'required',
            'category_id' => $isDraft ? '' : 'required',
            'start_at' => $isDraft ? 'nullable|date' : 'required|date',
            'end_at' => 'nullable|date|after:start_at',
            'display_time' => $isDraft ? 'nullable|date' : 'required|date',
            'description' => $isDraft ? '' : 'required',
        ];
    }
}
