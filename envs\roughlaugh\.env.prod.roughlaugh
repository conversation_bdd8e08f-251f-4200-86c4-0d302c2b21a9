APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:hYih9QXn/cK0mTEC1vXI2eR1/4k2jsdujCqjz+YEbSM=
APP_DEBUG=true
APP_URL=http://localhost
APP_TIMEZONE=Asia/Tokyo

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Connection Database
DB_CONNECTION=pgsql

# Admin DB
ADMIN_DB_HOST=roughlaugh-prd.cluster-crmk4cam4awx.ap-northeast-1.rds.amazonaws.com
ADMIN_DB_PORT=5432
ADMIN_DB_DATABASE=ttech_roughlaugh_admin
ADMIN_DB_USERNAME=roughlaugh_api
ADMIN_DB_PASSWORD=83fGGX4aKhJf

# RL DB
RL_DB_HOST=roughlaugh-prd.cluster-crmk4cam4awx.ap-northeast-1.rds.amazonaws.com
RL_DB_PORT=5432
RL_DB_DATABASE=ttech_roughlaugh
RL_DB_USERNAME=roughlaugh_api
RL_DB_PASSWORD=83fGGX4aKhJf

# Connection per site
RL_CONNECTION=pgsql_rl
ADMIN_CONNECTION=pgsql

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Connect mailler aws_ses
MAIL_MAILER=ses
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_SES_ACCESS_KEY_ID=********************
AWS_SES_SECRET_ACCESS_KEY=FjJ4zNoAvLsPcP46h2fITWG6shenAnG6YKPNgpr3
AWS_SES_DEFAULT_REGION=us-east-1

# Connection amz_s3_RL
AWS_ACCESS_KEY_ID_RL=
AWS_SECRET_ACCESS_KEY_RL=
AWS_DEFAULT_REGION_RL=ap-northeast-1
AWS_BUCKET_RL=roughlaugh-prd-resource
AWS_USE_PATH_STYLE_ENDPOINT_RL=true
AWS_URL_RL=https://cdn.fc.roughlaugh-official.com/


PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=2Jtw5UpCoIsTqlZW97gI7Q0xdD9v0JAVyAgU84MVRd1nX8VxK9ZTyn1qzzO2OS2S

LOG_PATH=/opt/ttech/ttech-admin-api-logs/laravel.log

ID_INCREASE_DEFAULT=0