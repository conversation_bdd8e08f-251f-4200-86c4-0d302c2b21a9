<?php

namespace App\Models\SiteManage;

use App\Models\SiteManage\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Category extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'category_name',
        'created_at',
        'updated_at',
    ];

    public static $fieldable = [
        'id',
        'category_name',
        'created_at',
        'updated_at',
    ];
    protected $table = 'categories';
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' =>'datetime:Y-m-d H:i:s'
    ];

    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fieldable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
