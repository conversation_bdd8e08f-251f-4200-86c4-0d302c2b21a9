<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminHasRole extends Model
{
    use HasFactory;

    protected $table = 'admin_has_roles';

    /**
     * The attributes.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'admin_id',
        'admin_role_id'
    ];

    public function role() {
        return $this->belongsTo(AdminRole::class, 'admin_role_id', 'id');
    }
}
