<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\BirthdayEmail;

class BirthdayEmailRepository
{
    /**
     * Get the first BirthdayEmail record.
     *
     * @return \App\Models\SiteManage\BirthdayEmail|null
     */
    public function get()
    {
        return BirthdayEmail::first();
    }

    /**
     * Update the BirthdayEmail record with the given ID.
     *
     * @param int $id The ID of the BirthdayEmail record to update.
     * @return void
     */
    public function update($id)
    {
        $record = BirthdayEmail::where('id', $id)->first();
        if ($record) {
            $record->save();
        }
    }
}