<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\DigitalMembershipCard;
use App\Shared\Constant;
use Illuminate\Support\Facades\DB;

/**
 * The DigitalMembershipCardRepository class is responsible for handling database operations related to digital membership cards.
 */
class DigitalMembershipCardRepository
{

    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'position';

    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'asc';

    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    public $digitalMembershipCard;

    public function __construct(DigitalMembershipCard $digitalMembershipCard)
    {
        $this->digitalMembershipCard = $digitalMembershipCard;
    }

    /**
     * Get list digital membership cards.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getList($param)
    {
        $q = DigitalMembershipCard::query();

        // Conditions are filtered by request
        $filters = [
            'id' => 'LIKE',
            'title' => 'LIKE',
            'status' => '='
        ];

        foreach ($filters as $key => $operator) {
            if (!empty($param[$key])) {
                $value = $operator == 'LIKE' ? '%' . $param[$key] . '%' : $param[$key];
                $q->where($key, $operator, $value);
            }
        }

        // Arrangement conditions
        if (!empty($param['sort'])) {
            $order = explode(',', $param['sort']);
            $column = $order[0] ?? self::DEFAULT_SORT_COLUMN;
            $direction = $order[1] ?? self::DEFAULT_SORT_TYPE;
            $q->orderBy($column, $direction);
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE)
                ->orderBy('created_at', 'desc');
        }

        // Pagination
        $size = !empty($param['size']) && (int)$param['size'] ? (int)$param['size'] : ($param['size'] == 'all' ? $q->count() : self::PAGING_SIZE);
        $digitalMembershipCard = $q->paginate($size);

        return [
            "data" => $digitalMembershipCard->items(),
            "per_page" => $digitalMembershipCard->perPage(),
            "total" => $digitalMembershipCard->total(),
            "current_page" => $digitalMembershipCard->currentPage(),
            "size" => $size
        ];
    }

    /**
     * Get a digital membership card by ID.
     *
     * @param int $id
     * @return \App\Models\SiteManage\DigitalMembershipCard|null
     */
    public function getById($id)
    {
        return DigitalMembershipCard::find($id);
    }

    /**
     * Create a new digital membership card.
     *
     * @param array $data
     * @return \App\Models\SiteManage\DigitalMembershipCard
     */
    public function create(array $data)
    {
        return DigitalMembershipCard::create($data);
    }

    /**
     * Update a digital membership card.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, array $data)
    {
        $card = DigitalMembershipCard::find($id);
        if ($card) {
            return $card->update($data);
        }
        return false;
    }

    /**
     * Update the position of digital membership cards.
     *
     * @param array $data
     * @return bool
     */
    public function updatePosition($data)
    {
        /**
         * Process data before update
         */
        $total = DigitalMembershipCard::count();
        $newData = array_fill(1, $total, null);
        foreach ($data as $e) {
            $position = ($e['position']);
            while ($newData[$position] != null) {
                if ($newData[$position]['id'] < $e['id']) {
                    $id = $newData[$position]['id'];
                    $newData[$position]['id'] = $e['id'];
                    $e['id'] = $id;
                }
                $position++;
            }
            $newData[$position] = [
                'id' => $e['id'],
                'position' => $position
            ];
        }

        $idsChange = collect($data)->pluck('id');
        $digitalMembershipCardPosition = DigitalMembershipCard::select('id', 'position')->whereNotIn('id', $idsChange)->orderBy('position', 'asc')->get();
        $idx = 1;
        foreach ($digitalMembershipCardPosition as $position) {
            while ($newData[$idx] != null) {
                $idx++;
            }
            $newData[$idx] = [
                'id' => $position->id,
                'position' => $idx
            ];
            $idx++;
        }

        $updated = true;
        DB::transaction(function () use ($newData, $updated) {
            for ($i = 1; $i <= count($newData); $i++) {
                $element = $newData[$i];
                $element['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
                $update = DigitalMembershipCard::where('id', $element['id'])->update($element);
                if (!$update) {
                    $updated = false;
                    DB::rollBack();
                }
            }
            return compact('updated');
        });

        return $updated;
    }

    /**
     * Delete a digital membership card.
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $card = DigitalMembershipCard::find($id);
        if ($card) {
            return $card->delete();
        }
        return false;
    }

    public function getCardsOrderByPosition()
    {
        return $this->digitalMembershipCard::query()->orderBy('position', 'asc')->orderBy('created_at', 'desc')->get();
    }

    /**
     * Check if there is only one default card.
     *
     * @return int
     */
    public function hasOnlyOneDefaultOn(array $data)
    {
        $count = DigitalMembershipCard::where('status', Constant::STATUS_DATA['PUBLIC'])
            ->where('default', Constant::STATUS_SETTING['ON'])
            ->when(!empty($data['id']), function ($query) use ($data) {
                return $query->where('id', '!=', $data['id']);
            })
            ->count();

        return $count;
    }
}
