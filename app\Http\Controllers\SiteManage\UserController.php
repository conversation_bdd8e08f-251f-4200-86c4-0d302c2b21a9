<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\User;
use App\Services\SiteManage\UserService;
use App\Shared\ErrorMessage\ErrorMessage;
use App\Shared\ValidateMessage\ValidateMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Shared\Constant;

class UserController extends Controller
{
    /**
     * @var User::class
     */
    protected $objectModel = User::class;
    /**
     * @var UserService
     */
    private $userService;

    /**
     * @param UserService $userService
     */
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * Get list user.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsers(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'searchName' => request('nameFilter'),
                'searchEmail' => request('emailFilter'),
                'searchNumber' => request('numberFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];

            $users = $this->userService->getList($param);

            return response()->json([
                'success' => true,
                'data' => $users
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Create user.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request), $this->arrayMessage($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::USER_REGIST_FAIL,
                    'data' => $validator->errors()
                ]);
            }
            $user = $this->userService->create($request->only(['username', 'email', 'gender', 'date_of_birth', 'password']));
            return response()->json([
                'id' => $user->id,
                'success' => $user->id ? true : false,
                'message' => $user->id ? 'Save Success' : 'Save Failed'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Get user detail.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            $user = $this->userService->detail($id);

            return response()->json([
                'success' => true,
                'data' => $user
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update user.
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->updateArrayRules($request, $id), $this->arrayMessage($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::USER_UPDATE_FAIL,
                    'data' => $validator->errors()
                ]);
            }

            $id = $this->userService->update($request->only(['username', 'email', 'gender', 'date_of_birth']), (int) $id);
            return response()->json([
                'id' => $id,
                'success' => $id ? true : false,
                'message' => $id ? 'Save Success' : 'Save Failed'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Delete a user.
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            $user = $this->userService->getUserGmoActiveByUserId($id);
            if ($user != null) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not available delete'
                ]);
            }

            $this->userService->delete($id);
            return response()->json([
                'success' => true,
                'message' => 'Delete Success'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return array
     */
    private function arrayRules(Request $request)
    {
        $connection = (new User())->getConnectionName();
        return [
            'username' => 'required|min:1|max:50|unique:' . $connection . '.users,username',
            'email' => 'required|email:rfc,dns|unique:' . $connection . '.users,email',
            'gender' => 'required',
            'date_of_birth' => 'required|date_format:Y-m-d',
            'password' => 'required|min:6|max:30',
            'confirm_password' => 'required|same:password',
        ];
    }

    /**
     * @param Request $request
     * @return array
     */
    private function updateArrayRules(Request $request, string $id)
    {
        $connection = (new User())->getConnectionName();
        return [
            'username' => 'required|min:1|max:50|unique:' . $connection . '.users,username,' . $id - config('app.id_increase_default') . ',id',
            'email' => 'required|email:rfc,dns|unique:' . $connection . '.users,email,' . $id - config('app.id_increase_default') . ',id',
            'gender' => 'required',
            'date_of_birth' => 'required|date_format:Y-m-d',
        ];
    }

    /**
     * @param Request $request
     * @return array
     */
    private function arrayMessage(Request $request)
    {
        return [
            'username.required' => ValidateMessage::USERNAME_REQUIRED,
            'username.max' => ValidateMessage::USERNAME_MAX_LENGTH,
            'username.min' => ValidateMessage::USERNAME_MIN_LENGTH,
            'username.unique' => ValidateMessage::USERNAME_UNIQUE,
            'email.required' => ValidateMessage::EMAIL_REQUIRED,
            'email.email' => ValidateMessage::EMAIL_FORMAT,
            'email.unique' => ValidateMessage::EMAIL_UNIQUE,
            'gender.required' => ValidateMessage::GENDER_REQUIRED,
            'date_of_birth.date_format' => ValidateMessage::DATE_OF_BIRTH_FORMAT,
            'date_of_birth.required' => ValidateMessage::DATE_OF_BIRTH_REQUIRED
        ];
    }
}
