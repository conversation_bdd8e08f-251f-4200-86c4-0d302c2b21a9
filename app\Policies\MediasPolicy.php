<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\SiteManage\Medias;

class MediasPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'VIEW') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'VIEW') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'ALL') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'ALL') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'ALL') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(Admin $admin, Medias $medias): bool
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(Admin $admin, Medias $medias): bool
    {
        //
    }
}
