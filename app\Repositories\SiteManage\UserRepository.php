<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\User;
use App\Models\SiteManage\UserGmoMember;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class UserRepository
{
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'created_at';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /*
     * USER_STATUS_INACTIVE
     */
    const USER_STATUS_INACTIVE = 'inactive';

    /*
     * USER_STATUS_ACTIVE
     */
    const USER_STATUS_ACTIVE = 'active';

    /*
     * USER_DELETE_EMAIL
     */
    const USER_DELETE_EMAIL = 'delete';

    /**
     * Get list user.
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList(array $param)
    {
        $q = User::query();
        $total = $q->count();
        if (isset($param['searchName'])) {
            $search = $param['searchName'];
            $q->when($param['searchName'], function ($query) use ($search) {
                $query->where('username', 'LIKE', '%' . $search . '%');
            });
        }

        if (isset($param['searchEmail'])) {
            $search = $param['searchEmail'];
            $q->when($param['searchEmail'], function ($query) use ($search) {
                $query->where('email', 'LIKE', '%' . $search . '%');
            });
        }

        if (isset($param['searchNumber'])) {
            $search = $param['searchNumber'];
            $q->when($param['searchNumber'], function ($query) use ($search) {
                $query->where(\DB::raw('(id + ' . config('app.id_increase_default') . ')'), 'LIKE', '%' . $search . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort'];
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0], $order[1]);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }

        if (isset($param['size']) && (int) $param['size']) {
            $users = $q->paginate((int) $param['size']);
        } else if ($param['size'] != 'all') {
            $users = $q->paginate(self::PAGING_SIZE);
        } else {
            $users = $q->paginate($total);
        }

        foreach ($users as $user) {
            $user->id = $user->id + config('app.id_increase_default');
            if ($user->gender == '1') {
                $user->gender = '男性';
            } else if ($user->gender == '2') {
                $user->gender = '女性';
            } else if ($user->gender == '3') {
                $user->gender = 'その他';
            } else {
                $user->gender = '回答しない';
            }
            // $user->created_at = $user->created_at;
        }

        return $users;
    }

    /**
     * Create user.
     * @param array $data
     */
    public function create(array $data)
    {
        $data['display_email'] = $data['email'];
        $data['date_of_birth'] = \Carbon\Carbon::parse($data['date_of_birth']);
        $data['status'] = self::USER_STATUS_ACTIVE;
        $created = User::create($data);

        return $created;
    }

    /**
     * Get user detail.
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function detail($id)
    {
        $user = User::where('id', $id - config('app.id_increase_default'))->first();
        $user->id = $user->id += config('app.id_increase_default');
        return $user;
    }

    /**
     * Update user.
     * @param array $data
     * @param int $id
     * @return array
     */
    public function update(array $data, int $id)
    {
        $data['date_of_birth'] = \Carbon\Carbon::parse($data['date_of_birth']);
        $updated = User::where('id', $id - config('app.id_increase_default'))->update($data);
        return [
            'success' => $updated && $updated > 0 ? true : false,
            'message' => $updated && $updated > 0 ? 'Save Success' : 'Save Failed'
        ];
    }

    /**
     * Delete user.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $user = User::find($id - config('app.id_increase_default'));
        $userGmo = UserGmoMember::query()->where('user_id', '=', $id - config('app.id_increase_default'))->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'ID is invalid'
            ]);
        }

        // if not yet stop auto payment then auto payment
        if (isset($userGmo->update_date) || isset($userGmo->plan)) {
            $userGmo->update_date = null;
            $userGmo->plan = null;
            $userGmo->save();
        }

        $user->status = self::USER_STATUS_INACTIVE;
        $user->email = self::USER_DELETE_EMAIL . "" . Carbon::now()->format('YmdHis') . "@gmail.com";
        $user->display_email = self::USER_DELETE_EMAIL . "" . Carbon::now()->format('YmdHis') . "@gmail.com";
        $deleted = $user->save();

        return $deleted;
    }
}
