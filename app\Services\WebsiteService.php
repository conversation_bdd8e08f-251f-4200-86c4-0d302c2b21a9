<?php

namespace App\Services;

use App\Repositories\AdminRepository;
use App\Repositories\WebsiteRepository;

class WebsiteService
{
    private WebsiteRepository $websiteRepository;

    public function __construct(WebsiteRepository $websiteRepository)
    {
        $this->websiteRepository = $websiteRepository;
    }

    /**
     * Get list website.
     *
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList()
    {
        return $this->websiteRepository->getList();
    }

    /**
     * get website by alias
     * @param string $alias
     */
    public function getWebsiteByAlias($alias) {
        return $this->websiteRepository->getWebsiteByAlias($alias);
    }

}
