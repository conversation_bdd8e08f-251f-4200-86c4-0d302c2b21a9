<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\MediaRepository;
use Illuminate\Support\Str;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use App\Services\S3Service;

/**
 * Medias Service.
 */
class MediaService
{
    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';
    /**
     * MESSAGE_ERROR_ID
     */
    const MESSAGE_ERROR_ID = 'Id is invalid';
    /**
     * MESSAGE_UPLOAD_FAILED
     */
    const MESSAGE_UPLOAD_FAILED = 'Upload file Failed';
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var MediaRepository
     */
    private $mediaRepository;

    /**
     * @var S3Service
     */
    private $s3Service;

    public function __construct(MediaRepository $mediaRepository, JWTAuth $jwtAuth, S3Service $s3Service)
    {
        $this->jwtAuth = $jwtAuth;
        $this->mediaRepository = $mediaRepository;
        $this->s3Service = $s3Service;
    }

    /**
     * Get list media.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $result = $this->mediaRepository->list($param);

        $perPage = $result['per_page'];
        $items = $result['data'];
        for ($i = 0; $i < $perPage; $i++) {
            if ($items[$i]["thumbnail_url"]) {
                $items[$i]["thumbnail_url"] = $this->s3Service->setPath($items[$i]["thumbnail_url"])->doGetLink();
            }
        }

        $result['data'] = $items;

        return $result;
    }

    /**
     * Get media detail.
     * @param integer $id
     * @return array
     */
    public function detail($id)
    {
        $media = $this->mediaRepository->detail($id);
        if ($media) {
            if ($media->thumbnail_url) {
                $media->thumbnail_url = $this->s3Service->setPath($media->thumbnail_url)->doGetLink();
            }
        }

        return [
            'success' => (bool)$media,
            'data' => $media,
            'message' => $media ? '' : self::MESSAGE_ERROR_ID
        ];
    }

    /**
     * @param $file
     * @return boolean
     */
    public function deleteFileS3($key)
    {
        return $this->s3Service->setPath($key)->doDelete();
    }

    /**
     * @param $file
     * @return string
     */
    public function uploadFileToS3($file)
    {
        $key = 'media/' . Str::uuid();
        if ($file) {
            $uploaded = $this->s3Service->setPath($key)->setContent(file_get_contents($file))->doUpload();
            if (!$uploaded) {
                return '';
            }

            return $key;
        }

        return '';
    }

    /**
     * Create media
     * @param array $param
     * @param \Illuminate\Http\UploadedFile $file
     * @return array
     */
    public function create($param, $file)
    {
        $param['created_by'] = $this->jwtAuth->user()->id;
        if (isset($file)) {
            $param['thumbnail_url'] = $this->uploadFileToS3($file);
            if ($param['thumbnail_url'] == '') {
                return [
                    'success' => false,
                    'message' => self::MESSAGE_UPLOAD_FAILED
                ];
            }
        }

        $created = $this->mediaRepository->create($param);
        return [
            'success' => $created,
            'message' => $created ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Update media
     * @param array $param
     * @param \Illuminate\Http\UploadedFile $file
     * @param $id
     * @return array
     */
    public function update($param, $file, $id)
    {
        $media = $this->mediaRepository->detail($id);
        if (!$media) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }
        if ($file) {
            $param['thumbnail_url'] = $this->uploadFileToS3($file);
            if ($param['thumbnail_url'] == '') {
                return [
                    'success' => false,
                    'message' => self::MESSAGE_UPLOAD_FAILED
                ];
            }
            $this->deleteFileS3($media->thumbnail_url);
        }

        $updated = $this->mediaRepository->update($param, $id);
        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Update media position
     * @param array $param
     * @param $id
     * @return array
     */
    public function updatePosition($param)
    {

        $updated = $this->mediaRepository->updatePosition($param);

        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Delete media
     * @param $id
     * @return array
     */
    public function delete($id)
    {
        $media = $this->mediaRepository->detail($id);
        if (!$media) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        $deleted = $this->mediaRepository->delete($id);

        return [
            'success' => $deleted,
            'message' => $deleted ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }
}
