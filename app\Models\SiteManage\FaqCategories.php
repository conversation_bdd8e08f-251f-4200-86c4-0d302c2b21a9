<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class FaqCategories extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'name',
        'created_at',
        'updated_at'
    ];
    public static $fielable = [
        'id',
        'name',
        'created_at',
        'updated_at'
    ];
    protected $table = 'faq_categories';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s'
    ];

    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fielable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
