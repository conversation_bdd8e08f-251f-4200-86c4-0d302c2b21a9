<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\BannerRepository;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use App\Services\S3Service;

class BannerService
{
    /**
     * MAX_BANNER_UPLOAD
     */
    const MAX_BANNER_UPLOAD = 10;
    /**
     * ERROR_MAX_UPLOAED
     */
    const ERROR_MAX_UPLOADED = 'error';
    /**
     * MESS_ERROR_UPLOAD
     */
    const MESS_ERROR_UPLOAD = 'Upload failed';
    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';
    /**
     * BANNER_STATUS_PUBLIC
     */
    const BANNER_STATUS_PUBLIC = 'public';
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var BannerRepository
     */
    private $bannerRepository;
    /**
     * @var S3Service
     */
    private $s3Service;

    /**
     * @param BannerRepository $bannerRepository
     * @param JWTAuth $jwtAuth
     * @param S3Service $s3Service
     */
    public function __construct(BannerRepository $bannerRepository, JWTAuth $jwtAuth, S3Service $s3Service)
    {
        $this->jwtAuth = $jwtAuth;
        $this->bannerRepository = $bannerRepository;
        $this->s3Service = $s3Service;
    }

    /**
     * Get list Banners.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        return $this->bannerRepository->list($param);
    }

    /**
     * Get banner detail.
     * @param mixed $id
     * @return object
     */
    public function detail($id)
    {
        return $this->bannerRepository->detail($id);
    }

    /**
     * Create Banner.
     * @return mixed
     */
    public function create($param)
    {
        $countUploaded = $this->bannerRepository->getCountPublicBanner();
        /**
         * check limit public banner when create with status is public
         */
        if ($param['status'] == self::BANNER_STATUS_PUBLIC) {
            if ($countUploaded >= self::MAX_BANNER_UPLOAD) {
                return self::ERROR_MAX_UPLOADED;
            }
        }

        /**
         * Upload file to s3
         */
        $urlUploaded = $this->s3Service->setPath('banner')->setContent($param['image_path'])->doUpload();
        if (!$urlUploaded) {
            return [
                'success' => false,
                'message' => self::MESS_ERROR_UPLOAD
            ];
        }
        $param['image_path'] = $urlUploaded;

        return $this->bannerRepository->create($param);
    }

    /**
     * Update Banner
     * @param $param
     * @param $id
     * @return mixed|string
     */
    public function update($param, $id)
    {
        if (!is_string($param['image_path'])) {
            $urlUploaded = $this->s3Service->setPath('banner')->setContent($param['image_path'])->doUpload();
            $param['image_path'] = $urlUploaded;
        }

        $banner = $this->detail($id);
        /**
         * check new status of banner, if public then check if count public banners > 10 then check if old status != public return error
         */
        if ($param['status'] == self::BANNER_STATUS_PUBLIC) {
            $countUploaded = $this->bannerRepository->getCountPublicBanner();
            if ($countUploaded >= self::MAX_BANNER_UPLOAD) {
                if ($banner->status != self::BANNER_STATUS_PUBLIC) {
                    return self::ERROR_MAX_UPLOADED;
                }
            }
        }

        return $this->bannerRepository->update($param, $id);
    }

    /**
     * Update banner position
     * @param array $param
     * @param $id
     * @return array
     */
    public function updatePosition($param)
    {

        $updated = $this->bannerRepository->updatePosition($param);

        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Delete Banner
     * @param $id
     * @return mixed
     */
    public function delete($id)
    {
        return $this->bannerRepository->delete($id);
    }
}
