<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class MemberMedia extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'member_id',
        'media_id'
    ];

    public static $fielable = [
        'id',
        'member_id',
        'media_id'
    ];
    protected $table = 'member_media';
    public static function getDataFromRequest($data)
    {
        $dataRow = [];
        foreach (self::$fielable as $field) {
            if (array_key_exists($field, $data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
