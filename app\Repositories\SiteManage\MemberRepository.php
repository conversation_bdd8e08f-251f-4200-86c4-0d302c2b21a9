<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\FavoriteHistory;
use App\Models\SiteManage\Members;
use App\Models\SiteManage\MemberMedia;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use App\Models\SiteManage\Medias;
use App\Models\SiteManage\UserFavorite;

/**
 * News Repository.
 */
class MemberRepository
{
    protected $table = 'members';

    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'created_at';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /**
     * Get list news.
     * @param array $param
     * @return LengthAwarePaginator
     */
    public function getList($param)
    {
        $q = Members::query();
        $total = $q->count();
        if (isset($param['name']) || isset($param['status']) || isset($param['id'])) {
            $name = $param['name'];
            $status = $param['status'];
            $id = $param['id'];
            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'LIKE', '%' . $id . '%');
            });

            $q->when($param['name'], function ($query) use ($name) {
                $query->where('name', 'LIKE', '%' . $name . '%');
            });
            $q->when($param['status'], function ($query) use ($status) {
                $query->where('status', '=', $status);
            });

        }
        if (isset($param['sort'])) {
            $sort = $param['sort']; //ASC,DESC
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0] ?? self::DEFAULT_SORT_COLUMN, $order[1] ?? self::DEFAULT_SORT_TYPE);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }


        if (isset($param['size']) && (int) $param['size']) {
            $members = $q->paginate((int) $param['size']);
        } else if ($param['size'] != 'all') {
            $members = $q->paginate(self::PAGING_SIZE);
        } else {
            $members = $q->paginate($total);
        }

        return $members;
    }

    /**
     * Create new member.
     * @param array $data
     * @return bool
     */
    public function create(array $data)
    {
        $member = Members::insert($data);
        if (!isset($member)) {
            return false;
        }
        return true;
    }

    /**
     * Delete member by id.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $member = $this->detail($id);
        if (!isset($member)) {
            return false;
        }

        $userFavorites = UserFavorite::query()->where('artist_id', $id)->get();
        foreach ($userFavorites as $userFavorite) {
            $userFavorite->delete();
        }

        $favoriteHistories = FavoriteHistory::query()->where('previous_artist_id', $id)->orWhere('current_artist_id', $id)->get();
        foreach ($favoriteHistories as $favoriteHistory) {
            $favoriteHistory->delete();
        }

        MemberMedia::where('member_id', $id)->delete();
        return $member->delete();
    }

    /**
     * Detail member by id.
     * @param int $id
     * @return Members|bool
     */
    public function show($id)
    {
        $member = $this->detail((int) $id);
        if (!isset($member)) {
            return false;
        }

        return $member;
    }

    /**
     * @param array $data
     * @param $id
     * @return mixed
     */
    public function update(array $data, $id)
    {
        return Members::where('id', (int) $id)->update($data);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function detail($id)
    {
        return Members::where('id', (int) $id)->first();
    }
}
