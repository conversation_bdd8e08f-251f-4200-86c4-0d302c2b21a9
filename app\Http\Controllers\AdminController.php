<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Services\AdminService;
use App\Shared\Constant;
use App\Shared\ErrorMessage\ErrorMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Shared\ValidateMessage\ValidateMessage;
use Tymon\JWTAuth\JWTAuth;

class AdminController extends Controller
{

    /**
     * @var $objectModel
     */
    protected $objectModel = Admin::class;

    /**
     * TIME_RESET_EXPIRE
     */
    const TIME_RESET_EXPIRE = 60;
    private AdminService $adminService;

    public function __construct(AdminService $adminService)
    {
        $this->adminService = $adminService;
    }

    /**
     * Get list user admin .
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAdmins(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'searchName' => request('nameFilter'),
                'searchEmail' => request('emailFilter'),
                'searchId' => request('idFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
                'role' => request('role'),
                'site' => request('site'),
            ];

            $admins = $this->adminService->getList($param);

            return response()->json([
                'success' => true,
                'data' => $admins
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Create admin.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            Log::info("Create admin!", $request->all());
            if (auth()->user()->can('create', Admin::class)) {
                $data = [
                    'username' => $request->username,
                    'email' => $request->email,
                    'password' => $request->password,
                    'confirm_password' => $request->confirm_password,
                    'role_id' => $request->role_id,
                    'site_id' => $request->site_id
                ];

                $validator = $this->validateInfo($data);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => ErrorMessage::USER_REGIST_FAIL,
                        'data' => $validator->errors()
                    ]);
                }
                $result = $this->adminService->create($data);
                return response()->json([
                    'success' => $result ? true : false,
                    'message' => $result ? 'Save Success' : 'Save Failed',
                    'data' => $result ? $result : ''
                ]);
            }
        }
        return response()->json([], 403);
    }


    /**
     * validate information create new admin
     * @param array $data
     */
    private function validateInfo(array $data)
    {
        $validator = Validator::make(
            $data,
            [
                'username' => 'required|min:1|max:50|unique:admins,username,' . Constant::ADMIN_STATUS_INACTIVE . ',status',
                'email' => 'required|email|unique:admins,email,' . Constant::ADMIN_STATUS_INACTIVE . ',status',
                'password' => 'required|min:6|max:30',
                'confirm_password' => 'required|same:password',
                'role_id' => 'required',
                'site_id' => 'required'
            ],
            [
                'username.required' => ValidateMessage::USERNAME_REQUIRED,
                'username.max' => ValidateMessage::USERNAME_MAX_LENGTH,
                'username.min' => ValidateMessage::USERNAME_MIN_LENGTH,
                'username.unique' => ValidateMessage::USERNAME_UNIQUE,
                'email.unique' => ValidateMessage::EMAIL_UNIQUE,
                'email.required' => ValidateMessage::EMAIL_REQUIRED,
                'email.email' => ValidateMessage::EMAIL_FORMAT,
                'password.required' => ValidateMessage::PASSWORD_REQUIRED,
                'password.min' => ValidateMessage::PASSWORD_MIN_LENGTH,
                'password.max' => ValidateMessage::PASSWORD_MAX_LENGTH,
                'confirm_password.required' => ValidateMessage::CONFIRM_PASSWORD_REQUIRED,
                'confirm_password.same' => ValidateMessage::CONFIRM_PASSWORD_NOT_SAME,
                'role_id.required' => ValidateMessage::ROLE_ID_REQUIRED,
                'site_id.required' => ValidateMessage::SITE_ID_REQUIRED
            ]
        );

        return $validator;

    }

    /**
     * Verify email for reset password.
     * @param Request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request)
    {
        Log::info("Reset password!");
        $email = request('email');
        $validator = Validator::make(
            $request->only('email'),
            [
                'email' => 'required|email'
            ],
            [
                'email.required' => ValidateMessage::EMAIL_REQUIRED,
                'email.email' => ValidateMessage::EMAIL_FORMAT
            ]
        );

        if ($validator->fails()) {
            return response()->json(
                [
                    'success' => false,
                    'error' => $validator->errors()
                ]
            );
        }
        $data = [
            'email' => request('email'),
            'domain' => $request->headers->get('Origin')
        ];
        $result = $this->adminService->resetPassword($data);

        if ($result) {
            return response()->json([
                'success' => true
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => Constant::INCORECT_EMAIL
        ]);
    }

    /**
     * Update the user's password.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePassword(Request $request)
    {
        Log::info("Update Password!");
        $validator = $this->validateResetPass($request);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => ValidateMessage::RESET_PASSWORD_FAILED,
                'error' => $validator->errors()
            ]);
        }
        $data = [
            'token' => request('tokenEmail'),
            'newPassword' => request('newPassword'),
            'email' => request('email')
        ];
        $result = $this->adminService->updatePassword($data);
        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => Constant::TOKEN_IS_INVALID
            ]);
        }
        return response()->json([
            'success' => true
        ]);
    }

    /**
     * validate ResetPass.
     * @param Request $request
     */
    public function validateResetPass(Request $request)
    {
        Log::info("Validate password!");
        $validator = Validator::make(
            $request->only('newPassword', 'confirmPassword'),
            [
                'newPassword' => 'required|min:6|max:30',
                'confirmPassword' => 'required|same:newPassword'
            ],
            [
                'newPassword.required' => ValidateMessage::NEW_PASSWORD_REQUIRED,
                'newPassword.min' => ValidateMessage::NEW_PASSWORD_MIN,
                'newPassword.max' => ValidateMessage::NEW_PASSWORD_MAX,
                'confirmPassword.required' => ValidateMessage::CONFIRM_PASSWORD_REQUIRED,
                'confirmPassword.same' => ValidateMessage::CONFIRM_PASSWORD_NOT_SAME
            ]
        );

        return $validator;
    }

    /**
     * List website.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRoles(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $roles = $this->adminService->getRoles();

            return response()->json([
                'success' => true,
                'data' => $roles
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * get detail admin infor
     * @param Request $request
     */
    public function getDetail(Request $request)
    {
        if ($this->hasPermission('view')) {
            $admin_id = $request->id;

            $result = $this->adminService->getDetail($admin_id);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * update admin infor
     * @param Request $request
     */
    public function updateAdminInfo(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            Log::info('updateAdminInfo' . json_encode($request->all()));
            $data = [
                'id' => (int) $id,
                'username' => $request->username,
                'password' => $request->password,
                'confirm_password' => $request->confirm_password,
                'email' => $request->email,
                'role_id' => $request->role_id,
                'site_id' => $request->site_id
            ];

            $validator = $this->validateUpdateInfor($data);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()
                ]);
            }

            $result = $this->adminService->updateInfo($data);

            return response()->json([
                'success' => $result ? true : false,
                'data' => $result ? $result : ''
            ]);
        }

        return response()->json([], 403);
    }

    /**
     * validate update info admin
     * @param array $data
     */
    private function validateUpdateInfor(array $data)
    {
        $id = (int) $data['id'];
        $validatorArray = [
            'username' => 'required|min:1|max:50|unique:admins,username,' . $id . ',id,status,' . Constant::ADMIN_STATUS_ACTIVE . '',
            'email' => 'required|email|unique:admins,email,' . $id . ',id,status,' . Constant::ADMIN_STATUS_ACTIVE . '',
            'password' => '',
            'confirm_password' => 'same:password',
            'role_id' => 'required',
            'site_id' => 'required'
        ];

        $validator = Validator::make(
            $data,
            $validatorArray,
            [
                'username.required' => ValidateMessage::USERNAME_REQUIRED,
                'username.max' => ValidateMessage::USERNAME_MAX_LENGTH,
                'username.min' => ValidateMessage::USERNAME_MIN_LENGTH,
                'username.unique' => ValidateMessage::USERNAME_UNIQUE,
                'email.unique' => ValidateMessage::EMAIL_UNIQUE,
                'email.required' => ValidateMessage::EMAIL_REQUIRED,
                'email.email' => ValidateMessage::EMAIL_FORMAT,
                'password.min' => ValidateMessage::PASSWORD_MIN_LENGTH,
                'password.max' => ValidateMessage::PASSWORD_MAX_LENGTH,
                'confirm_password.same' => ValidateMessage::CONFIRM_PASSWORD_NOT_SAME,
                'role_id.required' => ValidateMessage::ROLE_ID_REQUIRED,
                'site_id.required' => ValidateMessage::SITE_ID_REQUIRED
            ]
        );

        return $validator;
    }

    /**
     * delete admin
     * @param Request $request
     */
    public function deleteAdmin(Request $request)
    {

        if ($this->hasPermission('delete')) {
            $id = $request->id;

            $result = $this->adminService->deleteAdmin($id);

            return response()->json([
                'success' => $result ? true : false
            ]);
        }

        return response()->json([], 403);
    }


    /**
     * get current authent admin profile
     * @param Request $request
     */
    public function getMyProfile(Request $request, JWTAuth $jwtAuth)
    {
        $admin = $jwtAuth->user();
        Log::info('profile' . json_encode($admin));
        if ($admin) {
            $admin->role = $admin->adminRole->role->name;
            $admin->adminWebsites;
            foreach ($admin->adminWebsites as $webiste) {
                $webiste->website;
            }
            return response()->json([
                'success' => true,
                'data' => $admin
            ]);
        }

        return response()->json([
            'success' => false
        ]);
    }

    /**
     * update profile for current authent admin
     * @param Request $request
     */
    public function updateProfile(Request $request, JWTAuth $jWTAuth)
    {

        $admin = $jWTAuth->user();
        Log::info('update profile' . json_encode($admin));

        $validator = Validator::make(
            $request->only('email', 'password', 'username', 'confirm_password'),
            [
                'email' => 'required|email|unique:admins,email,' . $admin->id . ',id,status,' . Constant::ADMIN_STATUS_ACTIVE . '',
                'username' => 'required|min:1|max:50|unique:admins,username,' . $admin->id . ',id,status,' . Constant::ADMIN_STATUS_ACTIVE . '',
                'password' => 'nullable|min:6|max:30',
                'confirm_password' => 'nullable|same:password'
            ],
            [
                'email.unique' => ValidateMessage::EMAIL_UNIQUE,
                'email.required' => ValidateMessage::EMAIL_REQUIRED,
                'email.email' => ValidateMessage::EMAIL_FORMAT,
                'username.unique' => ValidateMessage::USERNAME_UNIQUE,
                'password.min' => ValidateMessage::PASSWORD_MIN_LENGTH,
                'password.max' => ValidateMessage::PASSWORD_MAX_LENGTH,
                'confirm_password.same' => ValidateMessage::CONFIRM_PASSWORD_NOT_SAME
            ]   
        );
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ]);
        }
        $data = [
            'id' => $admin->id,
            'email' => $request->email,
            'username' => $request->username
        ];
        if ($request->password) {
            $data['password'] = $request->password;
        }

        $result = $this->adminService->updateProfile($data);

        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }
}
