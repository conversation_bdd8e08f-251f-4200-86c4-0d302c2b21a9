<?php

namespace App\Http\Controllers\SiteManage;

use App\Services\SiteManage\MemberService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SiteManage\Members;
use Illuminate\Support\Facades\Validator;
use App\Shared\ValidateMessage\ValidateMessage;
use App\Shared\Constant;
use App\Models\SiteManage\User;

class MemberController extends Controller
{
    /**
     * @var Members::class
     */
    protected $objectModel = Members::class;

    /**
     * @var MemberService
     */
    private $memberService;

    /**
     * @param MemberService $memberService
     */
    public function __construct(MemberService $memberService)
    {
        $this->memberService = $memberService;
    }

    /**
     * Get list member.
     * @param array $data
     * @return  \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'name' => request('nameFilter'),
                'status' => request('statusFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];

            $member = $this->memberService->getList($param);
            return response()->json([
                'success' => true,
                'data' => $member
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Create new member.
     * @param Request $request
     * @return  \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request), $this->arrayMessage($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }

            $result = $this->memberService->create($request->only(['name', 'status']));
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::CREATE_MEMBER_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::CREATE_MEMBER_SUCCESS
            ]);
        }
        return response()->json([], 403);

    }

    /**
     * Delete member by id.
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            $result = $this->memberService->delete($id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::DELETE_MEMBER_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::DELETE_MEMBER_SUCCESS
            ]);
        }

        return response()->json([], 403);
    }

    /**
     * Detail member by id.
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        if ($this->hasPermission('view')) {
            $result = $this->memberService->show($id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::MEMBER_NOT_FOUND
                ]);
            }
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update member by id.
     * @param Request $request
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request), $this->arrayMessage($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $result = $this->memberService->update($request->only(['name', 'status']), $id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::UPDATE_MEMBER_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::UPDATE_MEMBER_SUCCESS
            ]);
        }
        return response()->json([], 403);
    }


    /**
     * @param Request $request
     * @return string[]
     */
    private function arrayRules(Request $request)
    {
        $connection = (new User())->getConnectionName();
        return [
            'name' => 'required|max:100|unique:' . $connection . '.members,name' . ($request->id ? ",$request->id" : ''),
            'status' => 'required',
        ];
    }

    /**
     * @param Request $request
     * @return array
     */
    private function arrayMessage(Request $request)
    {
        return [
            'name.unique' => ValidateMessage::MEMBER_NAME_UNIQUE,
        ];
    }
}
