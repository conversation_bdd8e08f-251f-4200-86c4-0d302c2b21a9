<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\EventRepository;
use App\Services\WebsiteService;
use App\Shared\Constant;
use Tymon\JWTAuth\JWTAuth;
use App\Services\S3Service;
use DB;

/**
 * Events Service.
 */
class EventService
{
    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';
    /**
     * MESSAGE_ERROR_ID
     */
    const MESSAGE_ERROR_ID = 'Id is invalid';
    /**
     * MESSAGE_UPLOAD_FAILED
     */
    const MESSAGE_UPLOAD_FAILED = '画像のアップロードに失敗しました';
    const MESSAGE_UPLOAD_INVALID_TYPE = '画像はPNG、JPEG、GIF形式の画像を選択してください。';
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var EventRepository
     */
    private $eventRepository;

    /**
     * @var WebsiteService
     */
    private $websiteService;

    /**
     * @var S3Service
     */
    private $s3Service;

    /**
     * PREVIEW_CODE_LENGTH
     */
    const PREVIEW_CODE_LENGTH = 8;

    public function __construct(EventRepository $eventRepository, JWTAuth $jwtAuth, WebsiteService $websiteService, S3Service $s3Service)
    {
        $this->jwtAuth = $jwtAuth;
        $this->eventRepository = $eventRepository;
        $this->websiteService = $websiteService;
        $this->s3Service = $s3Service;
    }

    /**
     * Get list event.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $result = $this->eventRepository->list($param);
        foreach ($result['data'] as $item) {
            $item->category = $item->categoryRelation ? $item->categoryRelation->category_name : '';
        }
        return $result;
    }

    /**
     * Get event detail.
     * @param integer $id
     * @return array
     */
    public function detail($id)
    {
        $event = $this->eventRepository->detail($id);

        /**
         * get website
         */
        $aliasWebsite = getallheaders()[Constant::KEY_SITE];

        $website = $this->websiteService->getWebsiteByAlias($aliasWebsite);

        if (isset($event->preview_code)) {
            $event->previewUrl = "{$website->domain}/news/preview/{$id}?preview_code={$event->preview_code}";
            $event->prodUrl = "{$website->domain}/news/{$id}";
        }
        return [
            'success' => (bool) $event,
            'data' => $event,
            'message' => $event ? '' : self::MESSAGE_ERROR_ID
        ];
    }

    /**
     * Create event
     * @param array $param
     * @return array
     */
    public function create($param)
    {
        $param['created_by'] = $this->jwtAuth->user()->id;

        /**
         * add preview_code  when create
         */
        $param['preview_code'] = $this->generatePreviewCode(self::PREVIEW_CODE_LENGTH);

        $created = DB::transaction(function () use ($param) {
            return $this->eventRepository->create($param);
        });
        
        return [
            'success' => $created ? true : false,
            'message' => $created ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Update event
     * @param array $param
     * @param integer $id
     * @return array
     */
    public function update($param, $id)
    {
        $event = $this->eventRepository->detail($id);
        if (!$event) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        /**
         * check if event dont have preview_code then add when update
         */
        if (!isset($event->preview_code)) {
            //$param['preview_code'] = Str::uuid()->toString();
            $param['preview_code'] = $this->generatePreviewCode(self::PREVIEW_CODE_LENGTH);
        }

        $updated = DB::transaction(function () use ($param, $id) {
            return $this->eventRepository->update($param, $id);
        });

        $result = [
            'success' => $updated && $updated > 0,
            'message' => $updated && $updated > 0 ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];

        return $result;
    }

    /**
     * Update event position
     * @param array $param
     * @param $id
     * @return array
     */
    public function updatePosition($param)
    {

        $updated = $this->eventRepository->updatePosition($param);

        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Upload File
     * @param $file
     * @return array
     */
    public function uploadImage($file, $type)
    {
        try {
            if ($file) {
                $allowedTypes = ['jpeg', 'jpg', 'png', 'gif'];
                $fileType = $file->extension();
                if (in_array($fileType, $allowedTypes)) {
                    $image_url = $this->uploadFileToS3WithUrl($file, $type);
                    return [
                        'url' => $image_url ? $image_url : self::MESSAGE_UPLOAD_FAILED,
                    ];
                } else {
                    return [
                        'error' => [
                            'message' => self::MESSAGE_UPLOAD_INVALID_TYPE
                        ]
                    ];
                }
            } else {
                return [
                    'error' => [
                        'message' => self::MESSAGE_UPLOAD_FAILED
                    ]
                ];
            }
        } catch (\Throwable $th) {
            return [
                'error' => [
                    'message' => self::MESSAGE_UPLOAD_FAILED
                ]
            ];
        }

    }

    /**
     * Delete event
     * @param integer $id
     * @return array
     */
    public function delete($id)
    {
        $event = $this->eventRepository->detail($id);
        if (!$event) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        $deleted = $this->eventRepository->delete($id);
        return [
            'success' => $deleted,
            'message' => $deleted ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * generate preview_code
     * @param $length
     */
    private function generatePreviewCode($length)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';
        $maxIndex = strlen($characters) - 1;

        for ($i = 0; $i < $length; $i++) {
            $randomIndex = mt_rand(0, $maxIndex);
            $code .= $characters[$randomIndex];
        }

        return $code;
    }

    /**
     * @param $file
     * @return string
     */
    public function uploadFileToS3WithUrl($file, $type)
    {
        $key = $type;
        if ($file) {
            $uploaded = $this->s3Service->setPath($key)->setContent($file)->doUpload();
            return $uploaded;
        }

        return '';
    }
}
