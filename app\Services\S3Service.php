<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use App\Shared\Constant;
use Illuminate\Support\Facades\Log;

class S3Service
{
    private $S3_URL;
    private $connection;
    private $urlUploaded;
    /**
     * @var $path
     */
    public $path;
    /**
     * @var $content
     */
    public $content;

    /**
     * Contructor
     */
    public function __construct()
    {
        $this->connection = $this->setConnect();
        $this->S3_URL = $this->setS3Url();
    }

    /**
     * set connect to S3
     */
    private function setConnect()
    {
        try {
            $dataHeaders = getallheaders();
            $connection = Constant::SITE_AND_S3_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
            return $connection;
        } catch (\Exception $e) {
            abort(403, 'You do not have permission');
        }
    }

    public function setDisk($disk)
    {
        $this->connection = $disk;
        $this->S3_URL = $this->setS3Url();
        return $this;
    }

    /**
     * set S3_URL
     */
    private function setS3Url()
    {
        try {
            $S3_URL = config("filesystems.disks.{$this->connection}.url");
            return $S3_URL;
        } catch (\Exception $e) {
            Log::info('Upload2S3Service setS3Url exception: ' . $e->getMessage());
        }
    }

    /**
     * @return false|string
     */
    public function doUpload()
    {
        /**
         * Check path & content of document
         */
        if (!$this->path || !$this->content) {
            return false;
        }

        if ($name = Storage::disk($this->connection)->put($this->path, $this->content)) {
            $this->urlUploaded = $this->S3_URL . $name;
            return $this->urlUploaded;
        }

        return false;
    }

    /**
     * get link from s3
     * @return false|string
     */
    public function doGetLink()
    {
        /**
         * Check path
         */
        if (!$this->path) {
            return false;
        }
        return Storage::disk($this->connection)->url($this->path);
    }

    /**
     * delete in s3
     * @return boolean
     */
    public function doDelete()
    {
        /**
         * Check path
         */
        if (!$this->path) {
            return false;
        }

        $this->path = str_replace($this->S3_URL, '', $this->path);

        return Storage::disk($this->connection)->delete($this->path);
    }

    /**
     * @param $path
     * @return $this
     */
    public function setPath($path)
    {
        $this->path = $path;
        return $this;
    }

    /**
     * @param $content
     * @return $this
     */
    public function setContent($content)
    {
        $this->content = $content;
        return $this;
    }

    public function getFile()
    {
        /**
         * Check path
         */
        if (!$this->path) {
            return false;
        }

        $this->path = str_replace($this->S3_URL, '', $this->path);

        return Storage::disk($this->connection)->get($this->path);
    }

    public function checkExist()
    {
        /**
         * Check path
         */
        if (!$this->path) {
            return false;
        }

        $this->path = str_replace($this->S3_URL, '', $this->path);

        return Storage::disk($this->connection)->exists($this->path);
    }
}
