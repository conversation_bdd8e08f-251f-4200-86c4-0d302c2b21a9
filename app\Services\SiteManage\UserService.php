<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\UserGmoMemberRepository;
use App\Repositories\SiteManage\UserRepository;
use Illuminate\Support\Facades\DB;

class UserService
{
    /**
     * UserRepository
     */
    private UserRepository $userRepository;

    /**
     * UserGmoRepository
     */
    private UserGmoMemberRepository $userGmoRepository;

    /**
     * @param UserRepository $userRepository
     * @param UserGmoMemberRepository $userGmoRepository
     */
    public function __construct(UserRepository $userRepository, UserGmoMemberRepository $userGmoRepository)
    {
        $this->userRepository = $userRepository;
        $this->userGmoRepository = $userGmoRepository;
    }

    /**
     * Get list user.
     *
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList(array $param)
    {
        return $this->userRepository->getList($param);
    }

    /**
     * Create user.
     * @param array $param
     */
    public function create($param)
    {
        return DB::transaction(function () use ($param) {
            return $this->userRepository->create($param);
        });
    }

    /**
     * Get user detail.
     * @param int $id
     * @return object
     */
    public function detail($id)
    {
        return $this->userRepository->detail($id);
    }

    /**
     * Update user.
     * @param array $param
     * @param int $id
     * @return array
     */
    public function update($param, $id)
    {
        $param['display_email'] = $param['email'];
        return $this->userRepository->update($param, $id);
    }

    /**
     * Delete user.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        return DB::transaction(function () use ($id) {
            return  $this->userRepository->delete($id);
        });
    }

    /**
     * Get user gmo active.
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function getUserGmoActiveByUserId($id)
    {
        return $this->userGmoRepository->getUserGmoActiveByUserId($id);
    }
}
