<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\UserGmoMemberRepository;
use App\Repositories\SiteManage\UserGmoTransactionRepository;
use App\Shared\Constant;
use Illuminate\Support\Facades\Log;

class UserPaymentService
{

    /**
     * DATE_FORMAT
     */
    const DATE_FORMAT = 'm/d/Y';
    /**
     * UserGmoRepository
     */
    private UserGmoMemberRepository $userGmoRepository;

    /**
     * UserGmoTransactionRepository
     */
    private UserGmoTransactionRepository $userGmoTransactionRepository;

    /**
     * @param UserGmoMemberRepository $userGmoRepository
     * @param UserGmoTransactionRepository $userGmoTransactionRepository
     */
    public function __construct(
        UserGmoMemberRepository $userGmoRepository,
        UserGmoTransactionRepository $userGmoTransactionRepository
    ) {

        $this->userGmoRepository = $userGmoRepository;
        $this->userGmoTransactionRepository = $userGmoTransactionRepository;
    }

    /**
     * get list user Gmo
     * @param array $data
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getListUserGmo(array $data)
    {

        $results = $this->userGmoRepository->getListUserGmo($data);
        $convertData = $results->getCollection();
        foreach ($convertData as $result) {
            $result->register_date = $result->register_date ? date(self::DATE_FORMAT, strtotime($result->register_date)) : '';
            $result->update_date = $result->update_date ? date(self::DATE_FORMAT, strtotime($result->update_date)) : '';
            $result->plan = $result->plan ?? '';
            $result->user_id = $result->user_id + config('app.id_increase_default');
        }

        $results->setCollection($convertData);
        return $results;
    }

    /**
     * get detail user payment
     * @param $id
     * @return array
     */
    public function getDetailUserPayment($id)
    {

        $userGmo = $this->userGmoRepository->getDetailUserGmo($id);
        Log::info('getDetailUserPayment: ' . json_encode($userGmo));
        if (!$userGmo) {
            return [
                'userGmo' => null,
                'gmoTransaction' => null
            ];
        }

        $userGmo->register_date = $userGmo->register_date ? date(self::DATE_FORMAT, strtotime($userGmo->register_date)) : '';
        $userGmo->update_date = $userGmo->update_date ? date(self::DATE_FORMAT, strtotime($userGmo->update_date)) : '';
        $gmoTransaction = null;
        if ($userGmo->user_id) {
            $gmoTransaction = $this->userGmoTransactionRepository->getLastTransactionByUserId($userGmo->user_id);
            if ($gmoTransaction) {
                $gmoTransaction->gmo_tran_date = $gmoTransaction->gmo_tran_date ? date(self::DATE_FORMAT . 'H:i:s', strtotime($gmoTransaction->gmo_tran_date)) : '';
            }
        }
        $userGmo->user_id = $userGmo->user_id + config('app.id_increase_default');
        $userGmo->user->id = $userGmo->user->id + config('app.id_increase_default');

        return [
            'userGmo' => $userGmo,
            'gmoTransaction' => $gmoTransaction
        ];

    }

    /**
     * get list transaction
     * @param array $data
     */
    public function getTransactions (array $data) {
        $results = $this->userGmoTransactionRepository->getTransactions($data);

        $convertData = $results->getCollection();
        foreach ($convertData as $result) {
            $result->gmo_tran_date = $result->gmo_tran_date ? date(self::DATE_FORMAT, strtotime($result->gmo_tran_date)) : '';
            $result->update_date = $result->update_date ? date(self::DATE_FORMAT, strtotime($result->update_date)) : '';
            $result->plan = $result->plan ?? '';
            $result->user_id = $result->user_id + config('app.id_increase_default');
        }

        $results->setCollection($convertData);
        return $results;
    }

    /**
     * get detail transaction
     * @param $id
     */
    public function getDetailTransaction($id) {

        $transaction = $this->userGmoTransactionRepository->getTransactionById($id);

        $transaction->user_id = $transaction->user_id + config('app.id_increase_default');

        return $transaction;
    }
}
