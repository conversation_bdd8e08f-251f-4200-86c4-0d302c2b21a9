APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:hYih9QXn/cK0mTEC1vXI2eR1/4k2jsdujCqjz+YEbSM=
APP_DEBUG=true
APP_URL=http://*************:9000/
APP_TIMEZONE=Asia/Tokyo

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Connection Database
DB_CONNECTION=pgsql


# Admin DB
ADMIN_DB_HOST=*************
ADMIN_DB_PORT=5432
ADMIN_DB_DATABASE=postgres
ADMIN_DB_USERNAME=postgres
ADMIN_DB_PASSWORD=1q2w3E*
ADMIN_DB_SCHEMA=ttech_admin

# WS DB
WS_DB_HOST=*************
WS_DB_PORT=5432
# WS_DB_DATABASE=ttech_ws
WS_DB_DATABASE=ws_prd_20240627
WS_DB_USERNAME=postgres
WS_DB_PASSWORD=1q2w3E*
WS_DB_SCHEMA=public

# FL DB
FL_DB_HOST=*************
FL_DB_PORT=5432
# FL_DB_DATABASE=ttech_fl_20240405
FL_DB_DATABASE=fl_prd_20240627
FL_DB_USERNAME=postgres
FL_DB_PASSWORD=1q2w3E*
FL_DB_SCHEMA=public

# Connection per site
FL_CONNECTION=pgsql_fl
WS_CONNECTION=pgsql_ws
ADMIN_CONNECTION=pgsql

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
# Connect mailler aws_ses
MAIL_MAILER=ses
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_SES_ACCESS_KEY_ID=********************
AWS_SES_SECRET_ACCESS_KEY=FjJ4zNoAvLsPcP46h2fITWG6shenAnG6YKPNgpr3
AWS_SES_DEFAULT_REGION=us-east-1

# Connection asw_s3_fl
AWS_ACCESS_KEY_ID_FL=********************
AWS_SECRET_ACCESS_KEY_FL=G4Y+1SaQFi4E5WPWR6SY7urg5alTKXC2MgqX3KQ+
AWS_DEFAULT_REGION_FL=ap-northeast-1
AWS_BUCKET_FL=finalist-arius
AWS_USE_PATH_STYLE_ENDPOINT_FL=true
AWS_URL_FL=https://finalist-arius.s3.amazonaws.com/

# Connection asw_s3_ws
AWS_ACCESS_KEY_ID_WS=********************
AWS_SECRET_ACCESS_KEY_WS=G4Y+1SaQFi4E5WPWR6SY7urg5alTKXC2MgqX3KQ+
AWS_DEFAULT_REGION_WS=ap-northeast-1
AWS_BUCKET_WS=finalist-arius-ws
AWS_USE_PATH_STYLE_ENDPOINT_WS=true
AWS_URL_WS=https://finalist-arius-ws.s3.amazonaws.com/

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=2Jtw5UpCoIsTqlZW97gI7Q0xdD9v0JAVyAgU84MVRd1nX8VxK9ZTyn1qzzO2OS2S

LOG_PATH=/opt/ttech/ttech-admin-api-logs/laravel.log

ID_INCREASE_DEFAULT=1000