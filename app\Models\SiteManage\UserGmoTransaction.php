<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserGmoTransaction extends BaseModel
{
    use HasFactory;

    protected $table = 'gmo_transactions';

    protected $fillable = [
        'id',
        'user_id',
        'shop_id',
        'order_id',
        'plan',
        'status',
        'amount',
        'gmo_access_id',
        'gmo_access_pass',
        'gmo_tran_id',
        'gmo_tran_date',
        'gmo_approve',
        'gmo_card_number',
        'payment_method',
        'register_date',
        'update_date',
        'deleted_at'
    ];
}
