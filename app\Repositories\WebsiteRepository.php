<?php

namespace App\Repositories;
use App\Models\SiteManage\UserGmoTransaction;
use App\Models\Website;

class WebsiteRepository {

    /**
     * Get list websites.
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList()
    {
        $q = Website::query();
        return $q->get();
    }

    /**
     * get website by alias
     * @param string $alias
     */
    public function getWebsiteByAlias($alias) {

        return Website::query()->where('alias', $alias)->first();
    }
}