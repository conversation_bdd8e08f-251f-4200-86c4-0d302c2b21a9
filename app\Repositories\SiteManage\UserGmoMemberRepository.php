<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\UserGmoMember;
use Illuminate\Support\Facades\Log;

class UserGmoMemberRepository
{
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'user_id';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /**
     * Get user gmo active.
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function getUserGmoActiveByUserId(int $id)
    {
        $user = UserGmoMember::where('user_id', $id - (int) config('app.id_increase_default'))->where('plan', '!=', NULL)->where('updated_at', '!=', NULL)->first();
        if ($user != null) {
            $user->user_id = $user->user_id += config('app.id_increase_default');
        }
        return $user;
    }

    /**
     * get list user gmo
     * @param array $data
     */
    public function getListUserGmo(array $data)
    {
        Log::info('UserGmoMemberRepository getListUserGmo: ' . json_encode($data));

        $q = UserGmoMember::query();
        $q = $q->join('users', 'users.id', '=', 'user_gmos.user_id')->select('users.username', 'user_gmos.*');

        if (isset($data['username']) || isset($data['plan']) || isset($data['register_date']) || isset($data['update_date'])) {
            $username = $data['username'];
            $plan = $data['plan'];
            $registerDate = $data['register_date'];
            $updateDate = $data['update_date'];

            $q = $q->when($username, function ($query) use ($username) {
                $query->where('users.username', 'LIKE', '%' . $username . '%');
            });
            $q = $q->when($plan, function ($query) use ($plan) {
                $query->where('user_gmos.plan', '=', $plan);
            });
            $q = $q->when($registerDate, function ($query) use ($registerDate) {
                $query->where('user_gmos.register_date', '=', $registerDate);
            });
            $q = $q->when($updateDate, function ($query) use ($updateDate) {
                $query->where('user_gmos.update_date', $updateDate);
            });
        }

        if (isset($data['sort'])) {
            $sort = $data['sort'];
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset ($order['0']) && $order['1']) {
                    $query->orderBy( $order[0] , $order[1]);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }

        if (isset($data['size']) && (int)$data['size']) {
            $userGmos = $q->paginate((int)$data['size']);
        } else {
            $userGmos = $q->paginate(self::PAGING_SIZE);
        }
        return $userGmos;
    }

    /**
     * @param $id
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getDetailUserGmo($id)
    {
        Log::info('UserGmoMemberRepository getDetailUserGmo: ' . $id);
        return UserGmoMember::query()->where('id', (int)$id)->with('user')->first();
    }

}
