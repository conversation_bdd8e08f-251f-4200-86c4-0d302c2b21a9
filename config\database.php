<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'pgsql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [
        // admin schema
        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('ADMIN_DB_HOST', '127.0.0.1'),
            'port' => env('ADMIN_DB_PORT', '5432'),
            'database' => env('ADMIN_DB_DATABASE'),
            'username' => env('ADMIN_DB_USERNAME'),
            'password' => env('ADMIN_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('ADMIN_DB_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ],
        // FL schema
        'pgsql_fl' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('FL_DB_HOST', '127.0.0.1'),
            'port' => env('FL_DB_PORT', '5432'),
            'database' => env('FL_DB_DATABASE'),
            'username' => env('FL_DB_USERNAME'),
            'password' => env('FL_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('FL_DB_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ],
        // WS schema
        'pgsql_ws' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('WS_DB_HOST', '127.0.0.1'),
            'port' => env('WS_DB_PORT', '5432'),
            'database' => env('WS_DB_DATABASE'),
            'username' => env('WS_DB_USERNAME'),
            'password' => env('WS_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('WS_DB_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ],
        // Kemono schema
        'pgsql_kmn' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('KMN_DB_HOST', '127.0.0.1'),
            'port' => env('KMN_DB_PORT', '5432'),
            'database' => env('KMN_DB_DATABASE'),
            'username' => env('KMN_DB_USERNAME'),
            'password' => env('KMN_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('KMN_DB_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ],
        // RL schema
        'pgsql_rl' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('RL_DB_HOST', '127.0.0.1'),
            'port' => env('RL_DB_PORT', '5432'),
            'database' => env('RL_DB_DATABASE'),
            'username' => env('RL_DB_USERNAME'),
            'password' => env('RL_DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('RL_DB_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
