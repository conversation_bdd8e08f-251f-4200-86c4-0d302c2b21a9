<?php

namespace App\Repositories;

use App\Models\Admin;
use App\Models\AdminPasswordReset;
use App\Models\AdminRole;
use App\Models\Website;
use App\Shared\Constant;
use Carbon\Carbon;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class AdminRepository
{
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'created_at';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 20;

    /**
     * TIME_RESET_EXPIRE
     */
    const TIME_RESET_EXPIRE = 60;

    /**
     * Get list admins.
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList(array $param)
    {
        $q = Admin::query()->select('admins.*');


        $q->where(function ($query) {
            $query->where('admins.status', '!=', Constant::ADMIN_STATUS_INACTIVE)->orWhere('admins.status', '=', NULL);
        });

        $q->with('adminRole.role');

        if (isset($param['site'])) {
            $site = $param['site'];
            $q->whereHas('adminWebsites', function ($query) use ($site) {
                $query->where('website_id', $site);
            });
        }

        if (isset($param['role'])) {
            $role = $param['role'];
            $q->whereHas('adminRole', function ($query) use ($role) {
                $query->where('admin_role_id', $role);
            });
        }

        if (isset($param['searchId'])) {
            $id = $param['searchId'];
            $q->when($param['searchId'], function ($query) use ($id) {
                $query->where('admins.id', 'LIKE', '%' . $id . '%');
            });
        }

        if (isset($param['searchName'])) {
            $name = $param['searchName'];
            $q->when($param['searchName'], function ($query) use ($name) {
                $query->where('admins.username', 'LIKE', '%' . $name . '%');
            });
        }

        if (isset($param['searchEmail'])) {
            $search = $param['searchEmail'];
            $q->when($param['searchEmail'], function ($query) use ($search) {
                $query->where('admins.email', 'LIKE', '%' . $search . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort'];
            $order = explode(',', $sort);
            if (isset($order[0]) && isset($order[1])) {
                if ($order[0] == 'role_name') {
                    $q->leftJoin('admin_has_roles', 'admin_has_roles.admin_id', '=', 'admins.id')
                        ->leftJoin('admin_roles', 'admin_roles.id', '=', 'admin_has_roles.admin_role_id')
                        ->orderBy('admin_roles.name', $order[1]);
                } else {
                    $q->orderBy($order[0], $order[1]);
                }

            }

        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }

        if (isset($param['size']) && (int)$param['size']) {
            $admins = $q->paginate((int)$param['size']);
        } else {
            $admins = $q->paginate(self::PAGING_SIZE);
        }

        Log::info('sql: ' . json_encode($q->toSql()));

        foreach ($admins->items() as $item) {
            $item->role_name = $item->adminRole ? $item->adminRole->role->name : '';
        }

        return $admins;
    }

    /**
     * Create admin.
     * @param array $data
     * @return bool
     */
    public function create(array $data)
    {
        Log::info('AdminRepository create: ' . json_encode($data));
        $data['password'] = Hash::make($data['password']);
        $data['status'] = Constant::ADMIN_STATUS_ACTIVE;
        $roleId = $data['role_id'] ?? 0;
        $role = AdminRole::query()->find((int)$roleId);

        if (!$role) {
            return false;
        }

        unset($data['role_id']);
        $siteIds = explode(',', $data['site_id'] ?? '');
        unset($data['site_id']);
        $admin = Admin::query()->create($data);
        $adminId = $admin->id;

        // TO DO need transaction/rollback/commit
        $admin->adminRole()->create([
            'admin_id' => $adminId,
            'admin_role_id' => $role->id
        ]);

        // Create relationship for site_id
        foreach ($siteIds as $siteId) {
            $site = Website::query()->find((int)$siteId);
            if ($site) {
                $admin->adminWebsites()->create([
                    'admin_id' => $adminId,
                    'website_id' => $siteId
                ]);
            }
        }

        return $adminId;

    }

    /**
     * Get admin detail.
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function detail($id)
    {
        $admin = Admin::query()
            ->join('admin_has_roles', 'admins.id', '=', 'admin_has_roles.admin_id')
            ->join('admin_roles', 'admin_roles.id', '=', 'admin_has_roles.admin_role_id')
            ->join('admin_websites', 'admin_websites.admin_id', '=', 'admins.id')
            ->join('websites', 'websites.id', '=', 'admin_websites.website_id')
            ->select('admins.*', 'admin_roles.id as role_id', 'admin_roles.name as role_name')
            ->where('admins.id', (int)$id)
            ->with('adminWebsites')
            ->first();
        return $admin;
    }

    /**
     * Update admin.
     * @param array $data
     */
    public function update(array $data)
    {
        Log::info('AdminRepository update: ' . json_encode($data));
        $siteIds = explode(',', $data['site_id']);
        $passwordNew = $data['password'];
        $admin = $this->detail($data['id']);
        $admin->username = $data['username'];
        $admin->email = $data['email'];
        if ($passwordNew) {
            $admin->password = Hash::make($passwordNew);
        }

        $role = AdminRole::query()->find((int)$data['role_id']);
        if (!$role) {
            return false;
        }

        if ($admin->adminRole->admin_role_id && $admin->adminRole->admin_role_id != $data['role_id']) {
            $admin->adminRole->admin_role_id = (int)$data['role_id'];
            $admin->adminRole->save();
        }

        foreach ($siteIds as $siteId) {
            $site = Website::query()->find((int)$siteId);
            if (!$site) {
                return false;
            }
        }

        $admin->adminWebsites()->delete();
        foreach ($siteIds as $siteId) {
            $admin->adminWebsites()->create([
                'admin_id' => $admin->id,
                'website_id' => $siteId
            ]);
        }
        $admin->save();
        $admin->adminWebsites;

        return $admin;
    }

    /**
     * Delete admin.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $admin = Admin::find($id);
        if (!$admin) {
            return false;
        }
        $admin->status = Constant::ADMIN_STATUS_INACTIVE;
        $deleted = $admin->save();
        return $deleted;
    }

    /**
     * Check email for reset password
     * @param array $data
     * @return boolean
     */
    public function resetPassword($data)
    {
        Log::info('Start: AdminRepository reset password' . json_encode($data));
        $admin = Admin::where('email', '=', $data['email'])->first();
        if (!isset($admin)) {
            return false;
        }

        // $passwordReset = new AdminPasswordReset;
        // $passwordReset->user_id = $admin->id;
        // $passwordReset->expired_at = $data['expired_at'];
        // $passwordReset->token = $data['token'];
        // $passwordReset->save();
        $data['user_id'] = $admin->id;
        AdminPasswordReset::create($data);
        Log::info('End: UserRepository resetPassword!');
        return true;
    }

    /**
     * Update password
     * @param array $data
     * @return bool
     */
    public function updatePassword(array $data)
    {
        Log::info('Start: UserRepository update password' . json_encode($data));

        $passwordReset = AdminPasswordReset::where('token', $data['token'])->first();
        // If not found return false
        if (!isset($passwordReset)) {
            return false;
        }
        // Token is expired
        if (Carbon::parse($passwordReset->expired_at)->addMinutes(self::TIME_RESET_EXPIRE)->isPast()) {
            //Delete record
            $passwordReset->delete();
            return false;
        }
        //Update new password
        $admin = Admin::find($passwordReset->user_id);
        $admin->password = $data['newPassword'];
        $admin->save();
        $passwordReset->delete();
        Log::info('End: UserRepository update password!');
        return true;
    }

    /**
     * update profile for current authent admin
     * @param array $data
     */
    public function updateProfile(array $data)
    {
        return Admin::query()->where('id', $data['id'])->update($data);
    }
}
