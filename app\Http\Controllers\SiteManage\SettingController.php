<?php

namespace App\Http\Controllers\SiteManage;

use App\Services\SiteManage\SettingService;
use App\Http\Controllers\Controller;

/**
 * The SettingController class is responsible for handling the HTTP requests related to settings.
 */
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * The SettingService instance.
     *
     * @var SettingService
     */
    protected $settingService;

    /**
     * Create a new instance of SettingController class.
     *
     * @param SettingService $settingService The SettingService instance.
     * @return void
     */
    public function __construct(SettingService $settingService)
    {
        $this->settingService = $settingService;
    }

    /**
     * Get all settings.
     *
     * @return \Illuminate\Http\JsonResponse The JSON response containing the settings.
     */
    public function getAllSetting()
    {
        // Call the getAll method from the SettingService
        $settings = $this->settingService->getAll();
        if ($settings) {
            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        }

        // Return the settings as a response
        return response()->json([
            'success' => false
        ]);
    }

    /**
     * Update the settings.
     *
     * @param Request $request The HTTP request.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the updated setting.
     */
    public function updateSetting(Request $request)
    {
        // Call the update method from the SettingService
        $updatedSetting = $this->settingService->updateAll($request->all());

        // Return the updated setting as a response
        if ($updatedSetting) {
            return response()->json([
                'success' => true,
                'data' => $updatedSetting
            ]);
        }
        return response()->json([
            'success' => false
        ]);
    }
}
