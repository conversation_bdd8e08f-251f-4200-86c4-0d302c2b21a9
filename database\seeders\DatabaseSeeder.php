<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Admin::factory()->create([
            'email' => '<EMAIL>',
            'username' => 'admin',
            'password' => Hash::make(123456),
            'status' => 'ACTIVE',
            'refresh_token' => 'xxxxx',
            'role' => 'Adminstration'
        ]);
    }
}
