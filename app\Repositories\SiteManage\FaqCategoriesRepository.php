<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\FaqCategories;
use App\Models\SiteManage\FaqsToFaqCategories;

class FaqCategoriesRepository
{
    protected $table = 'faqcategory';

    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'created_at';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;


    /**
     * Get list faq-category.
     * @param array $param
     * @return LengthAwarePaginator
     */
    public function getList($param)
    {
        $q = FaqCategories::query();
        $total = $q->count();
        if (isset($param['name']) || isset($param['id'])) {
            $name = $param['name'];
            $id = $param['id'];
            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'LIKE', '%' . $id . '%');
            });

            $q->when($param['name'], function ($query) use ($name) {
                $query->where('name', 'LIKE', '%' . $name . '%');
            });
        }
        if (isset($param['sort'])) {
            $sort = $param['sort']; //ASC,DESC
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0] ?? self::DEFAULT_SORT_COLUMN, $order[1] ?? self::DEFAULT_SORT_TYPE);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::DEFAULT_SORT_TYPE);
        }


        if (isset($param['size']) && (int) $param['size']) {
            $faq = $q->paginate((int) $param['size']);
        } else if ($param['size'] != 'all') {
            $faq = $q->paginate(self::PAGING_SIZE);
        } else {
            $faq = $q->paginate($total);
        }

        return $faq;
    }

    /**
     * Create new faq-category.
     * @param int $id
     * @return array
     */
    public function getListFaqsToFaqCategories($id)
    {
        $q = FaqCategories::query();
        $q = $q->join('faqs_to_faq_categories', 'faqs_to_faq_categories.faq_category_id', '=', 'faq_categories.id')->where('faq_categories.id', $id)->get();

        return $q;
    }

    /**
     * Create new faq-category.
     * @param array $data
     * @return bool
     */
    public function create(array $data)
    {
        $data = FaqCategories::getDataFromRequest($data);
        $created = FaqCategories::create($data);
        if (!$created) {
            return false;
        }
        return true;
    }

    /**
     * Delete faq-category by id.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $faq = $this->detail($id);
        if (!isset($faq)) {
            return false;
        }
        $faq->delete();

        $faqToFaqCategory = FaqsToFaqCategories::query();
        $faqToFaqCategory->when($id, function ($query) use ($id) {
            $query->where('faq_category_id', '=', $id);
        });

        if (!isset($faqToFaqCategory)) {
            return false;
        }

        $faqToFaqCategory->delete();

        return true;
    }

    /**
     * Detail faq-category by id.
     * @param int $id
     * @return FaqCategory|bool
     */
    public function show($id)
    {
        $faq = $this->detail((int) $id);
        if (!isset($faq)) {
            return false;
        }

        return $faq;
    }

    /**
     * @param array $data
     * @param $id
     * @return mixed
     */
    public function update(array $data, $id)
    {
        $faq_category = FaqCategories::find($id);
        if (!isset($faq_category)) {
            return false;
        }
        $faq_category->name = $data['name'];
        $faq_category->save();
        if ($faq_category) {
            return true;
        }
    }

    /**
     * @param $id
     * @return mixed
     */
    public function detail($id)
    {
        return FaqCategories::where('id', (int) $id)->first();
    }
}
