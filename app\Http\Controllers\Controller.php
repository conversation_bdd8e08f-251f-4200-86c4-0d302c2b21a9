<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    /**
     * @var $objectModel
     */
    protected $objectModel;

    /**
     * @param $action
     * @return bool
     */
    public function hasPermission($action): bool
    {
        return auth()->user()->can($action, $this->objectModel);
    }

    /**
     * @param $objectModel
     * @return $this
     */
    public function setObjectModel($objectModel)
    {
        $this->objectModel = $objectModel;
        return $this;
    }
}
