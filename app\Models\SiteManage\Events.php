<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Events extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'title',
        'thumbnail',
        'pc_result_image',
        'sp_result_image',
        'description',
        'start_at',
        'end_at',
        'created_by',
        'deleted_at',
        'created_at',
        'updated_at',
        'is_display_result',
        'status',
        'event_type',
        'category',
        'display_time',
        'category_id',
        'position',
        'preview_code'
    ];
    public static $fieldable = [
        'id',
        'title',
        'thumbnail',
        'pc_result_image',
        'sp_result_image',
        'description',
        'start_at',
        'end_at',
        'created_by',
        'deleted_at',
        'is_display_result',
        'status',
        'event_type',
        'category',
        'display_time',
        'category_id',
        'position',
        'preview_code'
    ];
    protected $table = 'events';
    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fieldable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }

    public function categoryRelation() {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
}
