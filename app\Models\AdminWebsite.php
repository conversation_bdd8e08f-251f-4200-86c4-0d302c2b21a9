<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminWebsite extends Model
{
    use HasFactory;

    protected $table = 'admin_websites';

    protected $fillable = [
        'admin_id',
        'website_id'
    ];

    public function website(){
        return $this->belongsTo(Website::class, 'website_id', 'id');
    }
}
