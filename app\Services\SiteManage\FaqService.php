<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\FaqRepository;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use DB;

/**
 * Faqs Service.
 */
class FaqService
{
    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';
    /**
     * MESSAGE_ERROR_ID
     */
    const MESSAGE_ERROR_ID = 'Id is invalid';
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var FaqRepository
     */
    private $faqRepository;

    /**
     * PREVIEW_CODE_LENGTH
     */
    const PREVIEW_CODE_LENGTH = 8;

    public function __construct(FaqRepository $faqRepository, JWTAuth $jwtAuth)
    {
        $this->jwtAuth = $jwtAuth;
        $this->faqRepository = $faqRepository;
    }

    /**
     * Get list faq.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $result = $this->faqRepository->list($param);
        foreach ($result['data'] as $item) {
            $item->category = $item->categoryRelation ? $item->categoryRelation->category_name : '';
        }
        return $result;
    }

    /**
     * Get faq detail.
     * @param integer $id
     * @return array
     */
    public function detail($id)
    {
        $faq = $this->faqRepository->detail($id);

        return [
            'success' => (bool) $faq,
            'data' => $faq,
            'message' => $faq ? '' : self::MESSAGE_ERROR_ID
        ];
    }

    /**
     * Create faq
     * @param array $param
     * @return array
     */
    public function create($param)
    {
        $param['created_by'] = $this->jwtAuth->user()->id;

        $created = DB::transaction(function () use ($param) {
            return $this->faqRepository->create($param);
        });
        return [
            'success' => $created ? true : false,
            'message' => $created ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Update faq
     * @param array $param
     * @param integer $id
     * @return array
     */
    public function update($param, $id)
    {
        $faq = $this->faqRepository->detail($id);
        if (!$faq) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        $updated = DB::transaction(function () use ($param, $id) {
            return $this->faqRepository->update($param, $id);
        });

        $result = [
            'success' => $updated && $updated > 0,
            'message' => $updated && $updated > 0 ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];

        return $result;
    }

    /**
     * Update faq position
     * @param array $param
     * @param $id
     * @return array
     */
    public function updatePosition($param)
    {

        $updated = $this->faqRepository->updatePosition($param);

        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Delete faq
     * @param integer $id
     * @return array
     */
    public function delete($id)
    {
        $faq = $this->faqRepository->detail($id);
        if (!$faq) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        $deleted = $this->faqRepository->delete($id);
        return [
            'success' => $deleted,
            'message' => $deleted ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }
}
