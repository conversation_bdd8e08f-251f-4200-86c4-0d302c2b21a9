<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\FaqCategories;
use App\Services\SiteManage\FaqCategoriesService;
use Illuminate\Support\Facades\Validator;
use App\Shared\ValidateMessage\ValidateMessage;
use App\Shared\Constant;
use Illuminate\Http\Request;

class FaqCategoriesController extends Controller
{
    /**
     * @var FaqCategories::class
     */
    protected $objectModel = FaqCategories::class;

    /**
     * @var FaqCategoriesService
     */
    private $faqCategoriesService;

    /**
     * @param FaqCategoriesService $faqCategoriesService
     */
    public function __construct(FaqCategoriesService $faqCategoriesService)
    {
        $this->faqCategoriesService = $faqCategoriesService;
    }

    /**
     * Get list faq-categories.
     * @param array $data
     * @return  \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'name' => request('nameFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];

            $faq = $this->faqCategoriesService->getList($param);
            return response()->json([
                'success' => true,
                'data' => $faq
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Create new fag-categories.
     * @param Request $request
     * @return  \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $connection = (new FaqCategories())->getConnectionName();
            $validator = Validator::make($request->all(), [
                'name' => 'required|min:1|max:300|unique:' . $connection . '.faq_categories,name'
            ], [
                'name.required' => ValidateMessage::FAQ_CATEGORY_NAME_REQUIRED,
                'name.min' => ValidateMessage::FAQ_CATEGORY_NAME_MIN,
                'name.max' => ValidateMessage::FAQ_CATEGORY_NAME_MAX,
                'name.unique' => ValidateMessage::FAQ_CATEGORY_NAME_UNIQUE
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $result = $this->faqCategoriesService->create($request->only(['name']));
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::CREATE_FAQ_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::CREATE_FAQ_CATEGORY_SUCCESS
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Delete faq-categories by id.
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            $result = $this->faqCategoriesService->delete($id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::DELETE_FAQ_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::DELETE_FAQ_CATEGORY_SUCCESS
            ]);
        }

        return response()->json([], 403);
    }

    /**
     * Detail faq-categories by id.
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        if ($this->hasPermission('view')) {
            $result = $this->faqCategoriesService->show($id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::FAQ_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update faq-categories by id.
     * @param Request $request
     * @param int $id
     * @return  \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $connection = (new FaqCategories())->getConnectionName();
            $validator = Validator::make($request->all(), [
                'name' => 'required|min:1|max:300|unique:' . $connection . '.faq_categories,name' . ($request->id ? ",$request->id" : '')
            ], [
                'name.required' => ValidateMessage::FAQ_CATEGORY_NAME_REQUIRED,
                'name.min' => ValidateMessage::FAQ_CATEGORY_NAME_MIN,
                'name.max' => ValidateMessage::FAQ_CATEGORY_NAME_MAX,
                'name.unique' => ValidateMessage::FAQ_CATEGORY_NAME_UNIQUE
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $result = $this->faqCategoriesService->update($request->only(['name']), $id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::UPDATE_FAQ_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::UPDATE_FAQ_CATEGORY_SUCCESS
            ]);
        }
        return response()->json([], 403);
    }


    /**
     * @param Request $request
     * @return string[]
     */
    private function arrayRules(Request $request)
    {
        return [
            'name' => 'required',
        ];
    }
}
