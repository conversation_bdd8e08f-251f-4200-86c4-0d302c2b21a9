<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Banners extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'start_at',
        'end_at',
        'transition_url',
        'image_path',
        'create_at',
        'update_at',
        'position',
        'status'
    ];

    public static $fieldable = [
        'id',
        'start_at',
        'end_at',
        'transition_url',
        'image_path',
        'create_at',
        'update_at',
        'position',
        'status'
    ];
    protected $table = 'banners';
    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fieldable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
