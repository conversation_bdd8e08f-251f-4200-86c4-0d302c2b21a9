<?php

namespace App\Models\SiteManage;

use App\Shared\Constant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * Base model class
 */
class BaseModel extends Model
{
    /**
     * @var string|null
     */
    protected $connection;

    /**
     * Create a new Eloquent model instance.
     *
     * @param array $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->bootIfNotBooted();

        $this->initializeTraits();

        $this->syncOriginal();

        $this->fill($attributes);

        $this->connection = $this->setConnectionSiteDatabase();
    }

    /**
     * @return string|void
     */
    public function setConnectionSiteDatabase()
    {
        try {
            $dataHeaders = getallheaders();
            return Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
        } catch (\Exception $e) {
            abort(401, 'You do not have permission');
        }
    }

    public static function beginTransaction()
    {
        $dataHeaders = getallheaders();
        $connection = Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
        DB::connection($connection)->beginTransaction();
    }
    public static function commit()
    {
        $dataHeaders = getallheaders();
        $connection = Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
        DB::connection($connection)->commit();
    }
    public static function rollback()
    {
        $dataHeaders = getallheaders();
        $connection = Constant::SITE_AND_CONNECTION[$dataHeaders[Constant::KEY_SITE]];
        DB::connection($connection)->rollback();
    }

    /**
     * casts timestamp to datetime
     */
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
