<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\BirthdayEmailRepository;
use Illuminate\Support\Facades\Log;

/**
 * The BirthdayEmailService class is responsible for managing birthday email functionality.
 */
class BirthdayEmailService
{

    private $birthdayEmailRepository;

    /**
     * Create a new BirthdayEmailService instance.
     *
     * @param BirthdayEmailRepository $birthdayEmailRepository The repository for birthday email data.
     */
    public function __construct(BirthdayEmailRepository $birthdayEmailRepository)
    {
        $this->birthdayEmailRepository = $birthdayEmailRepository;
    }

    /**
     * Get the birthday email data.
     *
     * @return mixed The birthday email data.
     */
    public function get()
    {
        Log::info('Start: BirthdayEmailService get');

        return $this->birthdayEmailRepository->get();
    }

    /**
     * Update all the birthday email data.
     *
     * @param array $data The updated data for the birthday email.
     * @return bool True if the update was successful, false otherwise.
     */
    public function updateAll(array $data)
    {
        Log::info('Start: BirthdayEmailService updateAll' . json_encode($data));

        try {

            $record = $this->birthdayEmailRepository->get();
            if ($record) {
                $record->status = $data['status'];
                $record->delivery_time = $data['delivery_time'];
                $record->title = $data['title'];
                $record->content = $data['content'];
                $record->save();
            } else {
                Log::error('Error: BirthdayEmailService updateAll - Record not found');
                return false;
            }

            return true;
        } catch (\Throwable $th) {
            Log::error('Error: BirthdayEmailService updateAll - ' . $th->getMessage());
            return false;
        }
    }

}
