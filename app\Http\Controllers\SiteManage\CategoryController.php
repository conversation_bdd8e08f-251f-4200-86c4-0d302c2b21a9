<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\Category;
use App\Services\SiteManage\CategoryService;
use App\Shared\Constant;
use App\Shared\ValidateMessage\ValidateMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Category Controller.
 *
 */
class CategoryController extends Controller
{
    /**
     * @var Category::class
     */
    protected $objectModel = Category::class;
    /**
     * @var CategoryService
     */
    private $categoryService;

    /**
     * @param CategoryService $categoryService
     */
    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * Get list categories.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'category_name' => request('nameFilter'),
                'id' => request('idFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];
            $categories = $this->categoryService->list($param);
            return response()->json([
                'success' => true,
                'data' => $categories
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Get categories detail.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            return response()->json($this->categoryService->detail($id));
        }
        return response()->json([], 403);
    }

    /**
     * Create a categories.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $connection = (new Category())->getConnectionName();
            $validator = Validator::make($request->all(), [
                'category_name' => 'required|min:1|max:100|unique:' . $connection . '.categories,category_name'
            ], [
                'category_name.required' => ValidateMessage::CATEGORY_NAME_REQUIRED,
                'category_name.min' => ValidateMessage::CATEGORY_NAME_MIN,
                'category_name.max' => ValidateMessage::CATEGORY_NAME_MAX,
                'category_name.unique'=> ValidateMessage::CATEGORY_NAME_UNIQUE
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $data = [
                'category_name' => request('category_name'),
                'status' => request('status'),
            ];
            $result = $this->categoryService->create($data);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::CREATE_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::CREATE_CATEGORY_SUCCESS
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update a categories.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $connection = (new Category())->getConnectionName();
            $validator = Validator::make($request->all(), [
                'category_name' => 'required|min:1|max:100|unique:' . $connection . '.categories,category_name'. ($request->id ? ",$request->id" : '')
            ], [
                'category_name.required' => ValidateMessage::CATEGORY_NAME_REQUIRED,
                'category_name.min' => ValidateMessage::CATEGORY_NAME_MIN,
                'category_name.max' => ValidateMessage::CATEGORY_NAME_MAX,
                'category_name.unique'=> ValidateMessage::CATEGORY_NAME_UNIQUE
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $data = [
                'category_name' => request('category_name')
            ];
            $result = $this->categoryService->update($data, $id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::UPDATE_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::UPDATE_CATEGORY_SUCCESS
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Delete a categories.
     * @param Request $request
     * @param mixed $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            $result = $this->categoryService->delete($id);
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ValidateMessage::DELETE_CATEGORY_FAILED
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => Constant::DELETE_CATEGORY_SUCCESS
            ]);
        }

        return response()->json([], 403);
    }
}
