<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Services\SiteManage\BannerService;
use App\Shared\ErrorMessage\ErrorMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\SiteManage\Banners;

class BannerController extends Controller
{
    /**
     * @var Banners::class
     */
    protected $objectModel = Banners::class;
    /**
     * @var BannerService
     */
    private $bannerService;

    /**
     * @param BannerService $bannerService
     */

    public function __construct(BannerService $bannerService)
    {
        $this->bannerService = $bannerService;
    }

    /**
     * Get list banners.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'searchTransitionUrl' => request('transitionURLFilter'),
                'status' => request('statusFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];
            $banners = $this->bannerService->list($param);
            return response()->json([
                'success' => true,
                'data' => $banners
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Get banner detail.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            $banner = $this->bannerService->detail($id);
            return response()->json([
                'success' => true,
                'data' => $banner
            ]);
        }
        return response()->json([], 403);
    }

    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::BANNER_CREATE_FAIL,
                    'data' => $validator->errors()
                ]);
            }
            $id = $this->bannerService->create($request->only(['start_at', 'end_at', 'image_path', 'transition_url', '1', 'status']));
            if ($id == 'error') {
                return response()->json([
                    'id' => 0,
                    'success' => false,
                    'message' => 'limit_banner_active'
                ]);
            }
            ;

            if (isset($id['success'])) {
                return response()->json($id);
            }

            return response()->json([
                'id' => $id,
                'success' => (boolean) $id,
                'message' => $id ? 'Save Success' : 'Save Failed'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::BANNER_UPDATE_FAIL,
                    'data' => $validator->errors()
                ]);
            }
            $result = $this->bannerService->update($request->only(['start_at', 'end_at', 'image_path', 'transition_url', 'status']), (int) $id);
            if ($result == 'error') {
                return response()->json([
                    'id' => 0,
                    'success' => false,
                    'message' => 'limit_banner_active'
                ]);
            }
            ;
            return response()->json([
                'id' => $id,
                'success' => $result,
                'message' => $result ? 'Update Success' : 'Update Failed'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update banner position.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePosition(Request $request)
    {
        if ($this->hasPermission('update')) {
            return response()->json($this->bannerService->updatePosition($request->data));
        }

        return response()->json([], 403);
    }

    /**
     * Delete a banner.
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            $this->bannerService->delete($id);
            return response()->json([
                'success' => true,
                'message' => 'Delete Success'
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return string[]
     */
    private function arrayRules(Request $request)
    {
        $isCheckImage = (is_string($request['image_path']));
        return [
            'transition_url' => 'nullable|regex:/^(https?:\/\/)?([a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+)(:[0-9]{1,5})?(\/[^\s]*)?$/' ,
            'start_at' => 'required',
            'end_at' => 'nullable|date|after:start_at',
            'image_path' => $isCheckImage ? '' : 'nullable|file|image',
            'status' => 'required',
        ];
    }
}
