<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_has_permissions', function (Blueprint $table) {
            $table->id()->startingValue(3000000000);
            $table->bigInteger('permission_id');
            $table->foreign('permission_id')->references('id')->on('admin_permissions');
            $table->bigInteger('role_id');
            $table->foreign('role_id')->references('id')->on('admin_roles');
            $table->bigInteger('admin_id');
            $table->foreign('admin_id')->references('id')->on('admins');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_has_permissions');
    }
};
