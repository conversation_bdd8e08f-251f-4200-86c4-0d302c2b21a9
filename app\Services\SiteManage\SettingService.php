<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\SettingRepository;
use Illuminate\Support\Facades\Log;

/**
 * The SettingService class is responsible for handling operations related to settings.
 */
class SettingService
{
    private $settingRepository;

    /**
     * Create a new SettingService instance.
     *
     * @param SettingRepository $settingRepository The setting repository instance.
     */
    public function __construct(SettingRepository $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    /**
     * Get all settings.
     *
     * @return array The array of all settings.
     */
    public function getAll()
    {
        return $this->settingRepository->getAll();
    }

    /**
     * Update all settings.
     *
     * @param array $data The array of settings data to be updated.
     * @return bool True if the update is successful, false otherwise.
     */
    public function updateAll(array $data)
    {
        Log::info('Start: SettingService updateAll' . json_encode($data));
        try {
            foreach ($data as $name => $status) {
                Log::info('Updating setting - Name: ' . $name . ' Status: ' . $status);
                if (in_array($name, ['BIRTHDAY_EMAIL', 'DIGITAL_MEMBERSHIP_CARD', 'PHOTO']) && in_array($status, ['on', 'off'])) {
                    $this->settingRepository->update($name, $status);
                } else {
                    Log::error('Invalid setting name or status');
                    return false;
                }
            }

            return true;
        } catch (\Throwable $th) {
            Log::error('Error: SettingService updateAll - ' . $th->getMessage());
            return false;
        }
    }
}