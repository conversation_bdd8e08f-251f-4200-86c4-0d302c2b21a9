<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\BaseModel;
use App\Models\SiteManage\FaqCategories;
use App\Models\SiteManage\Faqs;
use App\Models\SiteManage\FaqsToFaqCategories;

/**
 * Faqs Repository.
 */
class FaqRepository
{
    /**
     *
     */
    protected $table = 'faqs';
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'position';
    /**
     * SORT_ASC
     */
    const SORT_ASC = 'ASC';
    /**
     * SORT_DESC
     */
    const SORT_DESC = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /**
     * Get list faq.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $q = Faqs::query();
        $size = 0;
        if (isset($param['title']) || isset($param['status']) || isset($param['id'])) {
            $title = $param['title'];
            $status = $param['status'];
            $id = $param['id'];

            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'ILIKE', '%' . $id . '%');
            });
            $q->when($param['title'], function ($query) use ($title) {
                $query->where('title', 'ILIKE', '%' . $title . '%');
            });
            $q->when($param['status'], function ($query) use ($status) {
                $query->where('status', 'ILIKE', '%' . $status . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort'];
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset ($order['0']) && $order['1']) {
                    $query->orderBy($order[0], $order[1]);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::SORT_ASC);
            $q->orderBy('created_at', self::SORT_DESC);
        }

        if (isset($param['size']) && (int)$param['size']) {
            $faq = $q->paginate((int)$param['size']);
            $size = (int)$param['size'];
        } else if ($param['size'] != 'all') {
            $faq = $q->paginate(self::PAGING_SIZE);
            $size = self::PAGING_SIZE;
        } else {
            $faq = $q->paginate($q->count());
            $size = $q->count();
        }

        return [
            "data" => $faq->items(),
            "per_page" => count($faq->items()),
            "total" => $faq->total(),
            "current_page" => $faq->currentPage(),
            "size" => $size
        ];
    }

    /**
     * Get faq detail.
     * @param integer $id
     * @return \App\Models\SiteManage\Faqs
     */
    public function detail($id)
    {
        $id = (int)$id;
        $faq = Faqs::where('id', $id)->first();
        $faqCategoryIds = FaqsToFaqCategories::where('faq_id', $id)->pluck('faq_category_id')->toArray();
        if ($faqCategoryIds) {
            $faqCategories = FaqCategories::whereIn('id', $faqCategoryIds)->pluck('id')->toArray();
            $faq['categories'] = $faqCategories;
        } else {
            $faq['categories'] = [];
        }

        return $faq;
    }

    /**
     * Create faq
     * @param array $data
     */
    public function create(array $data)
    {
        /**
         * Process data before create
         */
        $faq = Faqs::getDataFromRequest($data);
        $faq['created_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $faq['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $faq['position'] = 1;

        $created = true;
        BaseModel::beginTransaction();
        try {
            $faq = Faqs::create($faq);
            if ($data['categories']) {
                $categories = explode(',', $data['categories']);
                foreach ($categories as $category) {
                    $faqCategories = FaqCategories::where('id', $category)->first();
                    if (!$faqCategories) {
                        $created = false;
                        BaseModel::rollBack();
                        break;
                    }
                    $faq_category = [
                        'faq_id' => $faq->id,
                        'faq_category_id' => $category,
                        'created_at' => \Carbon\Carbon::now()->toDateTimeString(),
                        'updated_at' => \Carbon\Carbon::now()->toDateTimeString()
                    ];
                    FaqsToFaqCategories::insert($faq_category);
                }
            }
            $faqPosition = Faqs::select('id', 'position', 'updated_at')->orderBy('position', 'asc')->orderBy('created_at', 'desc')->get();
            $idx = 1;
            foreach($faqPosition as $faqData) {
                $faqData->position = $idx;
                // $faqData['updated_at']= \Carbon\Carbon::now()->toDateTimeString();
                $faqData->save();
                $idx++;
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $created = false;
            BaseModel::rollback();
        }
        return $created;
    }

    /**
     * @param array $data
     * @param integer $id
     * @return boolean
     */
    public function update(array $data, $id)
    {
        /**
         * Process data before update
         */
        $faq = Faqs::getDataFromRequest($data);
        $faq['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();


        $updated = true;
        BaseModel::beginTransaction();
        try {
            Faqs::where('id', $id)->update($faq);

            FaqsToFaqCategories::where('faq_id', $id)->delete();
            if ($data['categories']) {
                $categories = explode(',', $data['categories']);
                foreach ($categories as $category) {
                    $faqCategories = FaqCategories::where('id', $category)->first();
                    if (!$faqCategories) {
                        $updated = false;
                        BaseModel::rollBack();
                        break;
                    }
                    $faq_category = [
                        'faq_id' => $id,
                        'faq_category_id' => $category,
                        'updated_at' => \Carbon\Carbon::now()->toDateTimeString()
                    ];
                    FaqsToFaqCategories::insert($faq_category);
                }
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $updated = false;
            BaseModel::rollBack();
        }

        return $updated;
    }

    /**
     * Update list faq position
     * @param array $data
     * @return boolean
     */
    public function updatePosition($data)
    {
        /**
         * Process data before update
         */
        $total = Faqs::count();
        $newData = array_fill(1, $total, null);
        foreach($data as $e) {
            $position = ($e['position']);
            while ($newData[$position] != null) {
                if ($newData[$position]['id'] < $e['id']) {
                    $id = $newData[$position]['id'];
                    $newData[$position]['id'] = $e['id'];
                    $e['id'] = $id;
                }
                $position++;
            }
            $newData[$position] = [
                'id'=>$e['id'],
                'position' => $position
            ];
        }

        $idsChange = collect($data)->pluck('id');
        $faqPosition = Faqs::select('id', 'position')->whereNotIn('id', $idsChange)->orderBy('position', 'asc')->get();
        $idx = 1;
        foreach($faqPosition as $position) {
            while ($newData[$idx] != null) {
                $idx++;
            }
            $newData[$idx] = [
                'id'=>$position->id,
                'position' => $idx
            ];
            $idx++;
        }

        $updated = true;
        BaseModel::beginTransaction();
        try {
            for($i = 1; $i <= count($newData); $i++) {
                $element = $newData[$i];
                $element['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
                Faqs::where('id', $element['id'])->update($element);
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $updated = false;
            BaseModel::rollBack();
        }

        return $updated;
    }

    public function checkPositionIsExist($data, $position)
    {
        foreach($data as $e) {
            if($e['position'] == $position) return true;
        }
        return false;
    }

    /**
     * @param integer $id
     * @return boolean
     */
    public function delete($id)
    {
        $deleted = true;
        BaseModel::beginTransaction();
        try {
            FaqsToFaqCategories::where('faq_id', $id)->delete();
            Faqs::where('id', (int)$id)->delete();
            BaseModel::commit();
        } catch (\Throwable $th) {
            $deleted = false;
            BaseModel::rollBack();
        }


        return $deleted;
    }

}
