<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\BaseModel;
use App\Models\SiteManage\Medias;
use App\Models\SiteManage\MemberMedia;
use App\Models\SiteManage\Members;

/**
 * Medias Repository.
 */
class MediaRepository
{
    /**
     *
     */
    protected $table = 'medias';
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'position';
    /**
     * SORT_ASC
     */
    const SORT_ASC = 'ASC';
    /**
     * SORT_DESC
     */
    const SORT_DESC = 'DESC';
    /*
     * PAGING_SIZE
    */
    const PAGING_SIZE = 10;

    /**
     * Get list media.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $q = Medias::query();
        $size = 0;
        if (isset($param['title']) || isset($param['status']) || isset($param['mediaType']) || isset($param['id'])) {
            $title = $param['title'];
            $status = $param['status'];
            $mediaType = $param['mediaType'];
            $id = $param['id'];

            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'ILIKE', '%' . $id . '%');
            });
            $q->when($param['title'], function ($query) use ($title) {
                $query->where('title', 'ILIKE', '%' . $title . '%');
            });
            $q->when($param['status'], function ($query) use ($status) {
                $query->where('status', 'ILIKE', '%' . $status . '%');
            });
            $q->when($param['mediaType'], function ($query) use ($mediaType) {
                $query->where('media_type', 'ILIKE', '%' . $mediaType . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort']; //ASC,DESC
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset($order['0']) && $order['1']) {
                    $query->orderBy($order[0] ?? self::DEFAULT_SORT_COLUMN, $order[1] ?? self::SORT_ASC);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::SORT_ASC);
            $q->orderBy('created_at', self::SORT_DESC);
        }
        if (isset($param['size']) && (int)$param['size']) {
            $media = $q->paginate((int)$param['size']);
            $size = (int)$param['size'];
        } else if ($param['size'] != 'all') {
            $media = $q->paginate(self::PAGING_SIZE);
            $size = self::PAGING_SIZE;
        } else {
            $media = $q->paginate($q->count());
            $size = $q->count();
        }
        $items = $media->items();
        $perPage = count($items);

        return [
            "data" => $items,
            "per_page" => $perPage,
            "total" => $media->total(),
            "current_page" => $media->currentPage(),
            "size" => $size
        ];
    }

    /**
     * Get media detail.
     * @param integer $id
     * @return \App\Models\SiteManage\Medias
     */
    public function detail($id)
    {
        $id = (int)$id;
        $media = Medias::where('id', $id)->first();
        $memberIds = MemberMedia::where('media_id', $id)->pluck('member_id')->toArray();
        $members = Members::whereIn('id', $memberIds)->where('status', 'active')->pluck('id')->toArray();
        $media['members'] = $members;

        return $media;
    }

    /**
     * Create a media
     * @param array $data
     * @param \Illuminate\Http\UploadedFile $file
     * @return boolean
     */
    public function create(array $data)
    {
        /**
         * Process data before create
         */
        $media = Medias::getDataFromRequest($data);
        $media['created_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $media['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
        $media['position'] = 1;

        $created = true;
        BaseModel::beginTransaction();
        try {
            $media = Medias::create($media);
            if ($data['members']) {
                $members = explode(',', $data['members']);
                foreach ($members as $member) {
                    $memberDetail = Members::where('id', $member)->first();
                    if (!$memberDetail) {
                        $created = false;
                        BaseModel::rollBack();
                        break;
                    }
                    $memberMedia = [
                        'member_id' => $member,
                        'media_id' => $media->id
                    ];
                    MemberMedia::insert($memberMedia);
                }
            }
            $mediaPosition = Medias::select('id', 'position', 'updated_at')->orderBy('position', 'asc')->orderBy('created_at', 'desc')->get();
            $idx = 1;
            foreach($mediaPosition as $mediaData) {
                $mediaData->position = $idx;
                // $mediaData['updated_at']= \Carbon\Carbon::now()->toDateTimeString();
                $mediaData->save();
                $idx++;
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $created = false;
            BaseModel::rollBack();
        }
        return $created;
    }

    /**
     * @param array $data
     * @param \Illuminate\Http\UploadedFile $file
     * @param integer $id
     * @return boolean
     */
    public function update(array $data, $id)
    {
        /**
         * Update a media
         * Process data before update
         */
        $media = Medias::getDataFromRequest($data);
        $media['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();


        $updated = true;
        BaseModel::beginTransaction();
        try {
            Medias::where('id', $id)->update($media);

            MemberMedia::where('media_id', $id)->delete();
            if ($data['members']) {
                $members = explode(',', $data['members']);
                foreach ($members as $member) {
                    $memberDetail = Members::where('id', $member)->first();
                    if (!$memberDetail) {
                        $updated = false;
                        BaseModel::rollBack();
                        break;
                    }
                    $memberMedia = [
                        'member_id' => $member,
                        'media_id' => $id
                    ];
                    MemberMedia::insert($memberMedia);
                }
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $updated = false;
            BaseModel::rollBack();
        }

        return $updated;
    }

    /**
     * Update list media position
     * @param array $data
     * @return boolean
     */
    public function updatePosition($data)
    {
        /**
         * Process data before update
         */
        $total = Medias::count();
        $newData = array_fill(1, $total, null);
        foreach($data as $e) {
            $position = ($e['position']);
            while ($newData[$position] != null) {
                if ($newData[$position]['id'] < $e['id']) {
                    $id = $newData[$position]['id'];
                    $newData[$position]['id'] = $e['id'];
                    $e['id'] = $id;
                }
                $position++;
            }
            $newData[$position] = [
                'id'=>$e['id'],
                'position' => $position
            ];
        }

        $idsChange = collect($data)->pluck('id');
        $moviePosition = Medias::select('id', 'position')->whereNotIn('id', $idsChange)->orderBy('position', 'asc')->get();
        $idx = 1;
        foreach($moviePosition as $position) {
            while ($newData[$idx] != null) {
                $idx++;
            }
            $newData[$idx] = [
                'id'=>$position->id,
                'position' => $idx
            ];
            $idx++;
        }

        $updated = true;
        BaseModel::beginTransaction();
        try {
            for($i = 1; $i <= count($newData); $i++) {
                $element = $newData[$i];
                $element['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
                Medias::where('id', $element['id'])->update($element);
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $updated = false;
            BaseModel::rollBack();
        }

        return $updated;
    }

    /**
     * Delete a media
     * @param integer $id
     * @return boolean
     */
    public function delete($id)
    {
        $deleted = true;
        BaseModel::beginTransaction();
        try {
            MemberMedia::where('media_id', $id)->delete();
            Medias::where('id', (int)$id)->delete();
            BaseModel::commit();
        } catch (\Throwable $th) {
            $deleted = false;
            BaseModel::rollBack();
        }

        return $deleted;
    }
}
