<?php


namespace App\Services;

use App\Models\Admin;
use App\Repositories\AdminRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Facades\JWTFactory;
use App\Shared\Constant;
use Illuminate\Support\Facades\Mail;
use App\Mail\ResetPasswordMail;
use App\Repositories\AdminRoleRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Hash;

class AdminService
{
    private AdminRepository $adminRepository;
    private AdminRoleRepository $adminRoleRepository;

    public function __construct(AdminRepository $adminRepository, AdminRoleRepository $adminRoleRepository)
    {
        $this->adminRepository = $adminRepository;
        $this->adminRoleRepository = $adminRoleRepository;
    }

    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';
    /**
     * MESSAGE_ERROR_ID
     */
    const MESSAGE_ERROR_ID = 'Id is invalid';

    /**
     * TIME_RESET_EXPIRE
     */
    const TIME_RESET_EXPIRE = 60;

    /**
     * Get list admin.
     *
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getList(array $param)
    {
        return $this->adminRepository->getList($param);
    }

    /**
     * Create admin.
     * @param array $param
     * @return bool
     */
    public function create($param)
    {
        return DB::transaction(function () use ($param) {
            return $this->adminRepository->create($param);
        });

    }

    /**
     * reset password
     *
     * @param array $data
     * @return boolean
     */
    public function resetPassword(array $data)
    {
        Log::info('Start: service resetPassword' . json_encode($data));
        $expiredAt = Carbon::now()->addMinutes(self::TIME_RESET_EXPIRE);
        $payload = JWTFactory::sub(uniqid())
            ->setTTL(self::TIME_RESET_EXPIRE)
            ->email($data['email'])
            ->make();
        if (is_null($expiredAt) || is_null($data['email']) || is_null($payload)) {
            return false;
        }
        $token = JWTAuth::encode($payload);
        $data['expired_at'] = $expiredAt;
        $data['token'] = $token->get();

        $result = $this->adminRepository->resetPassword($data);
        if ($result) {
            //send mail
            $link = $data['domain'] . '/reset?token=' . $token;
            $sendMail = [
                'link' => $link,
                'email' => $data['email']
            ];
            try {
                Mail::to($data['email'])->send(new ResetPasswordMail($sendMail));
                return $result;
            } catch (\Exception $e) {
                return false;
            }
        }
        Log::info('End: service resetPassword!');
        return false;
    }

    /**
     * update password
     *
     * @param array $data
     * @return boolean
     */
    public function updatePassword(array $data)
    {
        Log::info('Start: service updatePassword' . json_encode($data));
        if (!isset($data['email']) || !isset($data['token'])) {
            return false;
        }
        Log::info('End: service updatePassword!');
        return DB::transaction(function () use ($data) {
            return $this->adminRepository->updatePassword($data);
        });

    }

    /**
     * Get roles.
     *
     * @param array $param
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getRoles()
    {
        return $this->adminRoleRepository->getList();
    }

    /**
     * get Detail admin
     * @param $admin_id
     */
    public function getDetail($admin_id)
    {

        return $this->adminRepository->detail($admin_id);

    }

    /**
     * update admin infor
     * @param array $data
     */
    public function updateInfo(array $data)
    {
        Log::info('updateInfo service: ' . json_encode($data));
        $admin = $this->adminRepository->detail($data['id']);
        if (!$admin) {
            return [
                'success' => false,
                'message' => self::MESSAGE_ERROR_ID
            ];
        }

        $updated = DB::transaction(function () use ($data) {
            $this->adminRepository->update($data);
        });

        return [
            'success' => $updated ? true : false,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * delete admin
     * @param $id
     */
    public function deleteAdmin($id)
    {

        return DB::transaction(function () use ($id) {
            return $this->adminRepository->delete($id);
        });
    }

    /**
     * update profile for current authent admin
     * @param array $data
     */
    public function updateProfile(array $data)
    {
        return DB::transaction(
            function () use ($data) {
                if (isset ($data['password'])) {
                    $data['password'] = Hash::make($data['password']);
                }
                return $this->adminRepository->updateProfile($data);
            }
        );
    }
}
