<?php

namespace App\Http\Controllers\SiteManage;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\SiteManage\BirthdayEmailService;

/**
 * The BirthdayEmailController class is responsible for handling the HTTP requests related to birthday emails.
 */
class BirthdayEmailController extends Controller
{
    protected $birthdayEmailService;

    /**
     * Create a new instance of BirthdayEmailController.
     *
     * @param BirthdayEmailService $birthdayEmailService The BirthdayEmailService instance.
     * @return void
     */
    public function __construct(BirthdayEmailService $birthdayEmailService)
    {
        $this->birthdayEmailService = $birthdayEmailService;
    }

    /**
     * Get the birthday email.
     *
     * @return \Illuminate\Http\JsonResponse The JSON response containing the birthday email data.
     */
    public function getBirthdayEmail()
    {
        $birthdayEmail = $this->birthdayEmailService->get();

        if ($birthdayEmail) {
            return response()->json([
                'success' => true,
                'data' => $birthdayEmail
            ]);
        }

        return response()->json([
            'success' => false
        ]);
    }

    /**
     * Update the birthday email.
     *
     * @param Request $request The HTTP request object.
     * @return \Illuminate\Http\JsonResponse The JSON response indicating the success or failure of the update.
     */
    public function updateBirthdayEmail(Request $request)
    {
        $updateBirthdayEmail = $this->birthdayEmailService->updateAll($request->all());

        if ($updateBirthdayEmail) {
            return response()->json([
                'success' => true,
                'data' => $updateBirthdayEmail
            ]);
        }
        return response()->json([
            'success' => false
        ]);
    }
}
