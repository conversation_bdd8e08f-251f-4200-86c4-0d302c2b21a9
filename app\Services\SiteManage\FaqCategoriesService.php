<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\FaqCategoriesRepository;
use Illuminate\Support\Arr;
use Tymon\JWTAuth\JWTAuth;
use Illuminate\Support\Facades\DB;

class FaqCategoriesService
{

    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var FaqCategoriesRepository
     */
    private $faqCategoriesRepository;

    /**
     * @param FaqCategoriesRepository $faqCategoriesRepository
     * @param JWTAuth $jwtAuth
     */
    public function __construct(FaqCategoriesRepository $faqCategoriesRepository, JWTAuth $jwtAuth)
    {
        $this->jwtAuth = $jwtAuth;
        $this->faqCategoriesRepository = $faqCategoriesRepository;
    }

    /**
     * Get list faq-category
     * @param array $param
     * @return LengthAwarePaginator
     */
    public function getList($param)
    {
        return $this->faqCategoriesRepository->getList($param);
    }

    /**
     * Create new faq-category.
     * @param array $data
     * @return bool
     */
    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            return $this->faqCategoriesRepository->create($data);
        });
    }

    /**
     * Delete faq-category by id.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        return DB::transaction(function () use ($id) {
            return $this->faqCategoriesRepository->delete($id);
        });
    }

    /**
     * Detail faq-categories by id.
     * @param int $id
     * @return bool
     */
    public function show($id)
    {
        return $this->faqCategoriesRepository->show($id);
    }

    /**
     * Update faq-categories by id.
     * @param array $data
     * @param int $id
     * @return bool
     */
    public function update(array $data, $id)
    {
        return DB::transaction(function () use ($data, $id) {
            return $this->faqCategoriesRepository->update($data, $id);
        });
    }
}
