<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\UserGmoMember;
use App\Services\SiteManage\UserPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class UserGmoController extends Controller
{

    /**
     * @var $objectModel
     */
    protected $objectModel = UserGmoMember::class;

    /**
     * @var UserPaymentService
     */
    private UserPaymentService $userPaymentService;

    /**
     * contructor
     * @param UserPaymentService $userPaymentService
     */
    public function __construct(UserPaymentService $userPaymentService)
    {
        $this->userPaymentService = $userPaymentService;
    }

    /**
     * get list user gmo
     * @param Request $request
     */
    public function getListUserGmo(Request $request)
    {

        if ($this->hasPermission('viewAny')) {
            Log::info('UserGmoController getListUserGmo' . json_encode($request->all()));

            $data = [
                'user_id' => $request->userIdFilter,
                'plan' => $request->planFilter,
                'order_id' => $request->orderIdFilter,
                'size' => $request->size,
                'sort' => $request->sort
            ];

            $result = $this->userPaymentService->getTransactions($data);
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }

        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detailUserGmo(Request $request)
    {

        if ($this->hasPermission('view')) {
            $result = $this->userPaymentService->getDetailTransaction($request->id);
            if(is_null($result)) {
                return response()->json(['data' => 'Resource not found'], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }

        return response()->json([], 403);
    }
}
