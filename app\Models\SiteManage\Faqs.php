<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Faqs extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'title',
        'status',
        'position',
        'description',
        'created_at',
        'updated_at'
    ];
    public static $fieldable = [
        'id',
        'title',
        'status',
        'position',
        'description',
        'created_at',
        'updated_at'
    ];
    protected $table = 'faqs';
    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fieldable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
