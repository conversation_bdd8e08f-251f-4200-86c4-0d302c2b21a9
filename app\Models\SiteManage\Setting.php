<?php

namespace App\Models\SiteManage;

use App\Models\SiteManage\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class Setting
 *
 * @package App\Models
 *
 * @property int $id
 * @property string $name
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Setting extends BaseModel
{
    use HasFactory;

    protected $table = 'settings';

    protected $fillable = [
        'name',
        'status'
    ];
}