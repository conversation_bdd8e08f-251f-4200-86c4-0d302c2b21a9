<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\UserGmoTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;


class UserGmoTransactionRepository
{
    /**
     * DEFAULT_SORT_COLUMN_USER_ID
     */
    const DEFAULT_SORT_COLUMN_USER_ID = 'user_id';
    /**
     * DEFAULT_SORT_COLUMN_GMO_TRAN_DATE
     */
    const DEFAULT_SORT_COLUMN_GMO_TRAN_DATE = 'gmo_tran_date';
    /**
     * DEFAULT_SORT_TYPE
     */
    const DEFAULT_SORT_TYPE = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /**
     * DATE_FORMAT
     */
    const DATE_FORMAT = 'Y-m-d H:i:s';

    /**
     * get last transaction by userId
     * @param $userId
     */
    public function getLastTransactionByUserId($userId)
    {

        return UserGmoTransaction::where('user_id', (int) $userId)->orderByDesc('created_at')->take(1)->first();
    }

    /**
     * get list transaction
     * @param array $data
     */
    public function getTransactions(array $data)
    { {
            Log::info('UserGmoTransactionRepository getTransactions: ' . json_encode($data));

            $q = UserGmoTransaction::query();
            $q = $q->join('users', 'users.id', '=', 'gmo_transactions.user_id')->select('users.username', 'gmo_transactions.*');

            if (isset($data['plan']) || isset($data['order_id']) || isset($data['user_id'])) {
                $plan = $data['plan'];
                $userId = $data['user_id'];
                $orderId = $data['order_id'];

                $q = $q->when($userId, function ($query) use ($userId) {
                    $query->where(\DB::raw('(users.id + ' . config('app.id_increase_default') . ')'), 'LIKE', '%' . $userId . '%');
                });
                $q = $q->when($orderId, function ($query) use ($orderId) {
                    $query->where('gmo_transactions.order_id', 'LIKE', '%' . $orderId . '%');
                });
                $q = $q->when($plan, function ($query) use ($plan) {
                    $query->where('gmo_transactions.plan', '=', $plan);
                });
            }

            if (isset($data['sort'])) {
                $sort = $data['sort'];
                $order = explode(',', $sort);
                $q->when($sort, function ($query) use ($order) {
                    if (isset ($order['0']) && $order['1']) {
                        $query->orderBy($order[0], $order[1]);
                    }
                });
            } else {
                $q->orderBy(self::DEFAULT_SORT_COLUMN_USER_ID, self::DEFAULT_SORT_TYPE)->orderBy(self::DEFAULT_SORT_COLUMN_GMO_TRAN_DATE, self::DEFAULT_SORT_TYPE);
            }

            if (isset($data['size']) && (int) $data['size']) {
                $transactions = $q->paginate((int) $data['size']);
            } else {
                $transactions = $q->paginate(self::PAGING_SIZE);
            }
            return $transactions;
        }
    }

    /**
     * get transaction by id
     * @param $id
     */
    public function getTransactionById($id)
    {
        Log::info('UserGmoTransactionRepository getTransaction: ' . json_encode($id));

        $q = UserGmoTransaction::query();
        $q = $q->join('users', 'users.id', '=', 'gmo_transactions.user_id')->select('users.username', 'gmo_transactions.*');
        $transaction = $q->where('gmo_transactions.id', (int) $id)->first();

        return $transaction;
    }
}