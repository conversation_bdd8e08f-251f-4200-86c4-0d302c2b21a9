<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use App\Models\Admin;
use App\Models\SiteManage\Category;
use App\Policies\AdminPolicy;
use App\Policies\EventsPolicy;
use App\Policies\FaqsPolicy;
use App\Policies\MediasPolicy;
use App\Policies\MembersPolicy;
use App\Policies\UsersPolicy;
use App\Policies\BannerPolicy;
use App\Models\SiteManage\Events;
use App\Models\SiteManage\Medias;
use App\Models\SiteManage\Members;
use App\Models\SiteManage\User;
use App\Policies\CategoryPolicy;
use App\Models\SiteManage\Banners;
use App\Models\SiteManage\DigitalMembershipCard;
use App\Models\SiteManage\FaqCategories;
use App\Policies\DigitalMembershipCardPolicy;
use App\Policies\FaqCategoriesPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Medias::class => MediasPolicy::class,
        Events::class => EventsPolicy::class,
        Faqs::class => FaqsPolicy::class,
        Members::class => MembersPolicy::class,
        User::class => UsersPolicy::class,
        Category::class => CategoryPolicy::class,
        Admin::class => AdminPolicy::class,
        Banners::class => BannerPolicy::class,
        FaqCategories::class => FaqCategoriesPolicy::class,
        DigitalMembershipCard::class => DigitalMembershipCardPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        //
    }
}
