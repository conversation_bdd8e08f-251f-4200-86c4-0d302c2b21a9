APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:hYih9QXn/cK0mTEC1vXI2eR1/4k2jsdujCqjz+YEbSM=
APP_DEBUG=true
APP_URL=http://localhost
APP_TIMEZONE=Asia/Tokyo

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Connection Database
DB_CONNECTION=pgsql

# Admin DB
ADMIN_DB_HOST=stg-fc-postgresql.c4fzj9p8shzb.ap-northeast-1.rds.amazonaws.com
ADMIN_DB_PORT=5432
ADMIN_DB_DATABASE=ttech_admin
ADMIN_DB_USERNAME=postgres
ADMIN_DB_PASSWORD=rJEhRSvzGxvkNTfz3GMI

# WS DB
WS_DB_HOST=stg-fc-postgresql.c4fzj9p8shzb.ap-northeast-1.rds.amazonaws.com
WS_DB_PORT=5432
WS_DB_DATABASE=ttech_ws
WS_DB_USERNAME=postgres
WS_DB_PASSWORD=rJEhRSvzGxvkNTfz3GMI

# FL DB
FL_DB_HOST=stg-fc-postgresql.c4fzj9p8shzb.ap-northeast-1.rds.amazonaws.com
FL_DB_PORT=5432
FL_DB_DATABASE=postgres
FL_DB_USERNAME=postgres
FL_DB_PASSWORD=rJEhRSvzGxvkNTfz3GMI

# Connection per site
FL_CONNECTION=pgsql_fl
WS_CONNECTION=pgsql_ws
ADMIN_CONNECTION=pgsql

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Connect mailler aws_ses
MAIL_MAILER=ses
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_SES_ACCESS_KEY_ID=********************
AWS_SES_SECRET_ACCESS_KEY=FjJ4zNoAvLsPcP46h2fITWG6shenAnG6YKPNgpr3
AWS_SES_DEFAULT_REGION=us-east-1

# Connection asw_s3_fl
AWS_ACCESS_KEY_ID_FL=********************
AWS_SECRET_ACCESS_KEY_FL=mlZkkrvITmQ8hJBFU2aOgg+EsPbxf71WuwJBYq5g
AWS_DEFAULT_REGION_FL=ap-northeast-1
AWS_BUCKET_FL=stg-storage.finalist-official.jp
AWS_USE_PATH_STYLE_ENDPOINT_FL=true
AWS_URL_FL=https://stg-storage.finalist-official.jp/

# Connection asw_s3_ws
AWS_ACCESS_KEY_ID_WS=********************
AWS_SECRET_ACCESS_KEY_WS=ZMcF02i3yHQBIIN+pUiU0FZygp4GBPmvxb/jigyV
AWS_DEFAULT_REGION_WS=ap-northeast-1
AWS_BUCKET_WS=stg-storage-fc.whitescorpion.jp
AWS_USE_PATH_STYLE_ENDPOINT_WS=true
AWS_URL_WS=https://stg-storage-fc.whitescorpion.jp/


PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=2Jtw5UpCoIsTqlZW97gI7Q0xdD9v0JAVyAgU84MVRd1nX8VxK9ZTyn1qzzO2OS2S

LOG_PATH=/opt/ttech/ttech-admin-api-logs/laravel.log

ID_INCREASE_DEFAULT=1000