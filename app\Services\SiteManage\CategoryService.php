<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\CategoryRepository;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use Illuminate\Support\Facades\DB;

/**
 * News Service.
 */
class CategoryService
{
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var CategoryRepository
     */
    private $catgoriesRepository;

    public function __construct(CategoryRepository $catgoriesRepository, JWTAuth $jwtAuth)
    {
        $this->jwtAuth = $jwtAuth;
        $this->catgoriesRepository = $catgoriesRepository;
    }

    /**
     * Get list category.
     * @param array $param
     * @return
     */
    public function list($param)
    {
        return $this->catgoriesRepository->list($param);
    }

    /**
     * Get news detail.
     * @param integer $id
     * @return array
     */
    public function detail($id)
    {
        return $this->catgoriesRepository->detail($id);
    }

    /**
     * Create category
     * @param array $data
     * @return array
     */
    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            return $this->catgoriesRepository->create($data);
        });

    }

    /**
     * Update category
     * @param array $data
     * @param integer $id
     * @return
     */
    public function update(array $data, $id)
    {
        return DB::transaction(function () use ($data, $id) {
            return $this->catgoriesRepository->update($data, $id);
        });
    }

    /**
     * Delete category
     * @param integer $id
     * @return
     */
    public function delete($id)
    {
        return DB::transaction(function () use ($id) {
            return $this->catgoriesRepository->delete($id);
        });
    }
}
