<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\SiteManage\SettingController;
use App\Http\Controllers\SiteManage\AlbumController;
use App\Http\Controllers\SiteManage\BannerController;
use App\Http\Controllers\SiteManage\BirthdayEmailController;
use App\Http\Controllers\SiteManage\FaqController;
use App\Http\Controllers\SiteManage\MemberController;
use App\Http\Controllers\SiteManage\CategoryController;
use App\Http\Controllers\SiteManage\DigitalMembershipCardController;
use App\Http\Controllers\SiteManage\EventController;
use App\Http\Controllers\SiteManage\FaqCategoriesController;
use App\Http\Controllers\SiteManage\MovieController;
use App\Http\Controllers\SiteManage\UserController;
use App\Http\Controllers\SiteManage\UserGmoController;
use App\Http\Controllers\WebsiteController;
use App\Models\SiteManage\Album;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::group(['prefix' => '', 'middleware' => 'api'], function () {
    // Routes require token
    Route::group(['prefix' => 'admin', 'middleware' => 'jwt-auth'], function () {
        Route::get('/', [AdminController::class, 'getAdmins']);
        Route::post('/', [AdminController::class, 'create']);
        Route::get('/roles', [AdminController::class, 'getRoles']);
        Route::get('/my-profile', [AdminController::class, 'getMyProfile']);
        Route::post('/update-profile', [AdminController::class, 'updateProfile']);
        Route::get('/{id}', [AdminController::class, 'getDetail']);
        Route::put('/{id}', [AdminController::class, 'updateAdminInfo']);
        Route::delete('/{id}', [AdminController::class, 'deleteAdmin']);
    });

    Route::group(['prefix' => 'settings', 'middleware' => ['jwt-auth']], function () {
        Route::get('', [SettingController::class, 'getAllSetting']);
        Route::post('', [SettingController::class, 'updateSetting']);
    });

    Route::group(['prefix' => 'birthday-email', 'middleware' => ['jwt-auth']], function () {
        Route::get('', [BirthdayEmailController::class, 'getBirthdayEmail']);
        Route::post('', [BirthdayEmailController::class, 'updateBirthdayEmail']);
    });

    Route::group(['prefix' => 'digital-membership-cards', 'middleware' => ['authenticate']], function () {
        Route::get('', [DigitalMembershipCardController::class, 'getListDigitalMembershipCard']);
        Route::post('', [DigitalMembershipCardController::class, 'createDigitalMembershipCard']);
        Route::put('/position', [DigitalMembershipCardController::class, 'updatePositionDigitalMembershipCard']);
        Route::get('/{id}', [DigitalMembershipCardController::class, 'getDigitalMembershipCard']);
        Route::post('/{id}', [DigitalMembershipCardController::class, 'updateDigitalMembershipCard']);
        Route::delete('/{id}', [DigitalMembershipCardController::class, 'deleteDigitalMembershipCard']);
    });

    Route::group(['prefix' => 'events', 'middleware' => ['authenticate']], function () {
        Route::get('/', [EventController::class, 'index']);
        Route::get('/{id}', [EventController::class, 'detail']);
        Route::post('/', [EventController::class, 'create']);
        Route::put('/position', [EventController::class, 'updatePosition']);
        Route::post('/upload', [EventController::class, 'uploadImage']);
        Route::put('/{id}', [EventController::class, 'update']);
        Route::delete('/{id}', [EventController::class, 'delete']);
    });

    Route::group(['prefix' => 'movies', 'middleware' => ['authenticate']], function () {
        Route::get('/', [MovieController::class, 'index']);
        Route::get('/{id}', [MovieController::class, 'detail']);
        Route::post('/', [MovieController::class, 'create']);
        Route::put('/position', [MovieController::class, 'updatePosition']);
        Route::post('/{id}', [MovieController::class, 'update']);
        Route::delete('/{id}', [MovieController::class, 'delete']);
    });

    Route::group(['prefix' => 'faqs', 'middleware' => ['authenticate']], function () {
        Route::get('/', [FaqController::class, 'index']);
        Route::get('/{id}', [FaqController::class, 'detail']);
        Route::post('/', [FaqController::class, 'create']);
        Route::put('/position', [FaqController::class, 'updatePosition']);
        Route::post('/{id}', [FaqController::class, 'update']);
        Route::delete('/{id}', [FaqController::class, 'delete']);
    });

    Route::group(['prefix' => 'user', 'middleware' => ['authenticate']], function () {
        Route::get('/', [UserController::class, 'getUsers']);
        Route::post('/', [UserController::class, 'create']);
        Route::get('/{id}', [UserController::class, 'detail']);
        Route::put('/{id}', [UserController::class, 'update']);
        Route::delete('/{id}', [UserController::class, 'delete']);
    });


    Route::group(['prefix' => 'members', 'middleware' => ['authenticate']], function () {
        Route::get('', [MemberController::class, 'index']);
        Route::post('', [MemberController::class, 'create']);
        Route::delete('/{id}', [MemberController::class, 'delete']);
        Route::get('/{id}', [MemberController::class, 'show']);
        Route::put('/{id}', [MemberController::class, 'update']);
    });

    Route::group(['prefix' => 'banners', 'middleware' => ['authenticate']], function () {
        Route::get('/', [BannerController::class, 'index']);
        Route::post('/', [BannerController::class, 'create']);
        Route::get('/{id}', [BannerController::class, 'detail']);
        Route::post('/{id}', [BannerController::class, 'update']);
        Route::put('/position', [BannerController::class, 'updatePosition']);
        Route::delete('/{id}', [BannerController::class, 'delete']);
    });

    Route::post('/auth/login', [AuthController::class, 'login']);
    // Routes not require token
    Route::group(['prefix' => 'reset-password'], function () {
        Route::get('', [AdminController::class, 'resetPassword']);
        Route::post('', [AdminController::class, 'updatePassword']);
    });

    Route::group(['prefix' => 'website'], function () {
        Route::get('/', [WebsiteController::class, 'getWebsites']);
    });

    Route::group(['prefix' => 'categories', 'middleware' => ['authenticate']], function () {
        Route::get('', [CategoryController::class, 'index']);
        Route::get('/{id}', [CategoryController::class, 'detail']);
        Route::post('/', [CategoryController::class, 'create']);
        Route::put('/{id}', [CategoryController::class, 'update']);
        Route::put('/delete/{id}', [CategoryController::class, 'delete']);
    });

    Route::group(['prefix' => 'payments', 'middleware' => ['authenticate']], function () {
        Route::get('', [UserGmoController::class, 'getListUserGmo']);
        Route::get('/{id}', [UserGmoController::class, 'detailUserGmo']);
    });

    Route::group(['prefix' => 'faq-categories', 'middleware' => ['authenticate']], function () {
        Route::get('', [FaqCategoriesController::class, 'index']);
        Route::post('', [FaqCategoriesController::class, 'create']);
        Route::delete('/{id}', [FaqCategoriesController::class, 'delete']);
        Route::get('/{id}', [FaqCategoriesController::class, 'show']);
        Route::put('/{id}', [FaqCategoriesController::class, 'update']);
    });
});
