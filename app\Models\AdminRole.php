<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminRole extends Model
{
    use HasFactory;

    protected $table = 'admin_roles';

    /**
     * The attributes.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name'
    ];

    public function roleHasPermissions() {
        return $this->hasMany(RoleHasPermission::class, 'role_id', 'id');
    }
}
