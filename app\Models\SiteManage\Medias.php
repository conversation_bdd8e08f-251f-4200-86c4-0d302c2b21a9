<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Medias extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'id',
        'title',
        'thumbnail_url',
        'blur_thumbnail_url',
        'vimeo_thumbnail_uri',
        'vimeo_video_id',
        'type',
        'position',
        'status',
        'media_type',
        'display_time',
        'start_at',
        'end_at',
        'created_by',
        'created_at',
        'updated_at',
    ];
    public static $fieldable = [
      'id',
      'title',
      'thumbnail_url',
      'blur_thumbnail_url',
      'vimeo_thumbnail_uri',
      'vimeo_video_id',
      'type',
      'position',
      'status',
      'media_type',
      'display_time',
      'start_at',
      'end_at',
      'created_by',
      'created_at',
      'updated_at',
    ];
    protected $table = 'medias';
    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fieldable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
