<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\UserGmoMember;
use Illuminate\Auth\Access\Response;

class UserGmoMemberPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'VIEW') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Admin $admin): bool
    {
        $permissions = $admin->adminRole->role->roleHasPermissions;
        $isPermis = false;
        foreach ($permissions as $permission) {
            if ($permission->permission->name == 'VIEW') {
                $isPermis = true;
            }
        }

        return $isPermis;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Admin $admin): bool
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Admin $admin, UserGmoMember $adminGmoMember): bool
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Admin $admin, UserGmoMember $adminGmoMember): bool
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(Admin $admin, UserGmoMember $adminGmoMember): bool
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(Admin $admin, UserGmoMember $adminGmoMember): bool
    {
        //
    }
}
