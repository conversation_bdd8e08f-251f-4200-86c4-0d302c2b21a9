<?php

namespace App\Http\Controllers\SiteManage;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\SiteManage\DigitalMembershipCard;
use App\Services\SiteManage\DigitalMembershipCardService;
use App\Shared\ErrorMessage\ErrorMessage;
use App\Shared\ValidateMessage\ValidateMessage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Digital membership card controller.
 */
class DigitalMembershipCardController extends Controller
{
    protected $objectModel = DigitalMembershipCard::class;

    private $digitalMembershipCardServiceService;

    /**
     * Create a new controller instance.
     *
     * @param  DigitalMembershipCardService  $digitalMembershipCardServiceService
     * @return void
     */
    public function __construct(DigitalMembershipCardService $digitalMembershipCardServiceService)
    {
        $this->digitalMembershipCardServiceService = $digitalMembershipCardServiceService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getListDigitalMembershipCard(Request $request)
    {
        Log::info('DigitalMembershipCardController getListDigitalMembershipCard data: ' . json_encode($request->all()));
        if ($this->hasPermission('viewAny')) {

            $cards = $this->digitalMembershipCardServiceService->getList($request->all());
            return response()->json([
                'success' => true,
                'data' => $cards
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Create a new digital membership card.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createDigitalMembershipCard(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_CREATE_FAIL,
                    'error' => $validator->errors()
                ]);
            }

            if($this->digitalMembershipCardServiceService->hasOnlyOneDefaultOn($request->all())) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_UPDATE_FAIL,
                    'error' => 'HAS_ONLY_ONE_DEFAULT_ON'
                ]);
            }

            $card = $this->digitalMembershipCardServiceService->create($request->all());

            if ($card) {
                return response()->json([
                    'success' => true,
                    'data' => $card
                ]);
            }
            return response()->json([
                'success' => false,
                'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_CREATE_FAIL
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getDigitalMembershipCard($id)
    {
        if ($this->hasPermission('view')) {
            $card = $this->digitalMembershipCardServiceService->get($id);
            return response()->json([
                'success' => true,
                'data' => $card
            ]);
        }
        return response()->json([], 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateDigitalMembershipCard(Request $request, $id)
    {
        if ($this->hasPermission('update')) {

            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_UPDATE_FAIL,
                    'error' => $validator->errors()
                ]);
            }

            if($this->digitalMembershipCardServiceService->hasOnlyOneDefaultOn($request->all())) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_UPDATE_FAIL,
                    'error' => ValidateMessage::HAS_ONLY_ONE_DEFAULT_ON 
                ]);
            }

            $result = $this->digitalMembershipCardServiceService->update($id, $request->all());

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => ErrorMessage::DIGITAL_MEMBERSHIP_CARD_UPDATE_FAIL
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        }
        return response()->json([], 403);
    }


    /**
     * Update the position of digital membership cards.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updatePositionDigitalMembershipCard(Request $request)
    {
        if ($this->hasPermission('update')) {
            return response()->json($this->digitalMembershipCardServiceService->updatePosition($request->data));
        }

        return response()->json([], 403);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteDigitalMembershipCard($id)
    {
        if ($this->hasPermission('delete')) {
            $card = $this->digitalMembershipCardServiceService->delete($id);

            if (!$card) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delete digital membership card fail'
                ]);
            }
            return response()->json([
                'success' => true
            ]);
        }

        return response()->json([], 403);
    }

    /**
     * @return string[]
     */
    private function arrayRules(Request $request)
    {

        $isCheckImage = (is_string($request['image']));

        return [
            'title' => 'required|max:100',
            'image' => $isCheckImage ? '' : 'required|file|image',
            'status' => 'required'
        ];
    }
}
