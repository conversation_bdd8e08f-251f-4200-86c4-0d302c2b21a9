<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;
use <PERSON>mon\JWTAuth\JWTAuth;

class JwtVerifyMiddleware
{
    private $jwtAuth;
    public function __construct(JWTAuth $jwtAuth) {
        $this->jwtAuth = $jwtAuth;
    }
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $this->jwtAuth->parseToken()->authenticate();
        } catch (JWTException $e) {
            
            return response()->json(['success' => $e->getMessage()], 401);
        }

        return $next($request)
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', '*')
        ->header('Access-Control-Allow-Headers', '*');
    }
}
