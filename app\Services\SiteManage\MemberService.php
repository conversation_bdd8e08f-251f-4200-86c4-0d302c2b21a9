<?php


namespace App\Services\SiteManage;

use App\Repositories\SiteManage\MemberRepository;
use <PERSON><PERSON>\JWTAuth\JWTAuth;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

/**
 * Member Service.
 */
class MemberService
{
    /**
     * @var JWTAuth
     */
    private $jwtAuth;
    /**
     * @var MemberRepository
     */
    private $memberRepository;

    /**
     * @param MemberRepository $memberRepository
     * @param JWTAuth $jwtAuth
     */
    public function __construct(MemberRepository $memberRepository, JWTAuth $jwtAuth)
    {
        $this->jwtAuth = $jwtAuth;
        $this->memberRepository = $memberRepository;
    }

    /**
     * Get list member
     * @param array $param
     * @return LengthAwarePaginator
     */
    public function getList($param)
    {
        return $this->memberRepository->getList($param);
    }

    /**
     * Create new member.
     * @param array $data
     * @return bool
     */
    public function create(array $data)
    {
        return $this->memberRepository->create($data);
    }

    /**
     * Delete member by id.
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        if ((int)$id) {
            return DB::transaction(function () use ($id) {
                return $this->memberRepository->delete($id);
            });
        }

        return false;
    }

    /**
     * Detail member by id.
     * @param int $id
     * @return bool
     */
    public function show($id)
    {
        return $this->memberRepository->show($id);
    }

    /**
     * Update member by id.
     * @param array $data
     * @param int $id
     * @return bool
     */
    public function update(array $data, $id)
    {
        if ((int)$id) {
            return $this->memberRepository->update($data, $id);
        }

        return false;
    }
}
