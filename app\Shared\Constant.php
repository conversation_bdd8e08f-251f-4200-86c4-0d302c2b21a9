<?php

namespace App\Shared;

final class Constant
{
    /**
     * KEY_SITE key of param site  FL,WS,...
     */
    public const KEY_SITE = 'Site';
    /**
     * AUTHENTICATION_FAILED
     */
    public const AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED";
    /**
     * INCORECT_EMAIL_OR_PASSWORD
     */
    public const INCORECT_EMAIL_OR_PASSWORD = "INCORECT_EMAIL_OR_PASSWORD";
    /**
     * SITE_AND_CONNECTION
     */
    public const SITE_AND_CONNECTION = ['FL' => 'pgsql_fl', 'WS' => 'pgsql_ws', 'KMN' => 'pgsql_kmn', 'RL' => 'pgsql_rl'];
    /**
     * SITE_CONNECTION
     */
    public const SITE_CONNECTION = "SITE_CONNECTION";

    public const INCORECT_EMAIL = "INCORECT_EMAIL";

    public const TOKEN_IS_INVALID = "TOKEN_IS_INVALID";

    public const CREATE_MEMBER_SUCCESS = 'CREATE_MEMBER_SUCCESS';

    public const DELETE_MEMBER_SUCCESS = 'DELETE_MEMBER_SUCCESS';

    public const UPDATE_MEMBER_SUCCESS = 'UPDATE_MEMBER_SUCCESS';

    public const CREATE_FAQ_CATEGORY_SUCCESS = 'CREATE_FAQ_CATEGORY_SUCCESS';

    public const DELETE_FAQ_CATEGORY_SUCCESS = 'DELETE_FAQ_CATEGORY_SUCCESS';

    public const UPDATE_FAQ_CATEGORY_SUCCESS = 'UPDATE_FAQ_CATEGORY_SUCCESS';

    public const CREATE_CATEGORY_SUCCESS = 'CREATE_CATEGORY_SUCCESS';

    public const DELETE_CATEGORY_SUCCESS = 'DELETE_CATEGORY_SUCCESS';

    public const UPDATE_CATEGORY_SUCCESS = 'UPDATE_CATEGORY_SUCCESS';

    /*
     * ADMIN_STATUS_INACTIVE
     */
    public const ADMIN_STATUS_ACTIVE = 'active';

    public const ADMIN_STATUS_INACTIVE = 'inactive';

    /**
     * Site
     */
    public const SITE_AND_S3_CONNECTION = [
        'FL' => 's3_fl',
        'WS' => 's3_ws',
        'KMN' => 's3_kmn',
        'RL' => 's3_rl'
    ];

    public const STATUS_SETTING = [
        'ON' => 'on',
        'OFF' => 'off'
    ];

    public const STATUS_DATA = [
        'PUBLIC' => 'public',
        'PRIVATE' => 'private'
    ];

}
