<?php

namespace App\Http\Controllers;

use App\Services\WebsiteService;
use Illuminate\Http\Request;
use <PERSON>mon\JWTAuth\JWTAuth;

class WebsiteController extends Controller
{

    private WebsiteService $websiteService;

    public function __construct(WebsiteService $websiteService)
    {
        $this->websiteService = $websiteService;
    }

    /**
     * List website.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWebsites(Request $request)
    {
        $websites = $this->websiteService->getList();

        return response()->json([
            'success' => true,
            'data' => $websites
        ]);
        return response()->json([], 401);
    }
}
