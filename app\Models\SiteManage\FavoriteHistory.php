<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class FavoriteHistory extends BaseModel
{
    use HasFactory;

    /**
     * @var string|null
     */
    protected $connection;

    protected $table = 'favorite_history';

    protected $fillable = [
        'id',
        'user_id',
        'previous_artist_id',
        'current_artist_id',
        'start_date',
        'end_date',
        'created_at',
        'updated_at'
    ];
}
