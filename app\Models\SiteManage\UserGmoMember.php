<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserGmoMember extends BaseModel
{
    use HasFactory;

    /**
     * STATUS_IS_PROCESSING
     */
    const STATUS_IS_PROCESSING = 'processing';
    /**
     * STATUS_IS_PENDING
     */
    const STATUS_IS_PENDING = 'pending';

    protected $table = 'user_gmos';

    protected $fillable = [
        'id',
        'site_id',
        'gmo_id',
        'user_id',
        'gmo_card_number',
        'gmo_card_seq',
        'card_seq_logical',
        'plan',
        'payment_method',
        'register_date',
        'expire',
        'forward',
        'default_flag',
        'update_date',
        'status',
        'count_fail',
        'deleted_at'
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
