<?php

namespace App\Repositories\SiteManage;

use App\Models\SiteManage\BaseModel;
use App\Models\SiteManage\Events;

/**
 * Events Repository.
 */
class EventRepository
{
    /**
     *
     */
    protected $table = 'events';
    /**
     * DEFAULT_SORT_COLUMN
     */
    const DEFAULT_SORT_COLUMN = 'position';
    /**
     * SORT_ASC
     */
    const SORT_ASC = 'ASC';
    /**
     * SORT_DESC
     */
    const SORT_DESC = 'DESC';
    /*
     * PAGING_SIZE
     */
    const PAGING_SIZE = 10;

    /**
     * Get list event.
     * @param array $param
     * @return array
     */
    public function list($param)
    {
        $q = Events::query();
        $size = 0;
        if (isset($param['title']) || isset($param['status']) || isset($param['eventType']) || isset($param['id'])) {
            $title = $param['title'];
            $status = $param['status'];
            $eventType = $param['eventType'];
            $id = $param['id'];

            $q->when($param['id'], function ($query) use ($id) {
                $query->where('id', 'ILIKE', '%' . $id . '%');
            });
            $q->when($param['title'], function ($query) use ($title) {
                $query->where('title', 'ILIKE', '%' . $title . '%');
            });
            $q->when($param['status'], function ($query) use ($status) {
                $query->where('status', 'ILIKE', '%' . $status . '%');
            });
            $q->when($param['eventType'], function ($query) use ($eventType) {
                $query->where('event_type', 'ILIKE', '%' . $eventType . '%');
            });
        }

        if (isset($param['sort'])) {
            $sort = $param['sort'];
            $order = explode(',', $sort);
            $q->when($sort, function ($query) use ($order) {
                if (isset ($order['0']) && $order['1']) {
                    $query->orderBy($order[0], $order[1]);
                }
            });
        } else {
            $q->orderBy(self::DEFAULT_SORT_COLUMN, self::SORT_ASC);
            $q->orderBy('created_at', self::SORT_DESC);
        }

        $q->with('categoryRelation:id,category_name');

        if (isset($param['size']) && (int)$param['size']) {
            $event = $q->paginate((int)$param['size']);
            $size = (int)$param['size'];
        } else if ($param['size'] != 'all') {
            $event = $q->paginate(self::PAGING_SIZE);
            $size = self::PAGING_SIZE;
        } else {
            $event = $q->paginate($q->count());
            $size = $q->count();
        }

        return [
            "data" => $event->items(),
            "per_page" => count($event->items()),
            "total" => $event->total(),
            "current_page" => $event->currentPage(),
            "size" => $size
        ];
    }

    /**
     * Get event detail.
     * @param integer $id
     * @return \App\Models\SiteManage\Events
     */
    public function detail($id)
    {
        return Events::where('id', (int)$id)->first();
    }

    /**
     * Create event
     * @param array $data
     */
    public function create(array $data)
    {
        $event = Events::getDataFromRequest($data);
        $created = true;
        try {
            BaseModel::beginTransaction();
            $created = Events::query()->create($event);
            if ($created) {
                $eventPosition = Events::select('id', 'position', 'updated_at')->orderBy('position', 'asc')->orderBy('created_at', 'desc')->get();
                $idx = 1;
                foreach($eventPosition as $event) {
                    $event->position = $idx;
                    // $event['updated_at']= \Carbon\Carbon::now()->toDateTimeString();
                    $event->save();
                    $idx++;
                }
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $created = false;
            BaseModel::rollBack();
        }
        return $created;
    }

    /**
     * @param array $data
     * @param integer $id
     * @return boolean
     */
    public function update(array $data, $id)
    {
        /**
         * Process data before update
         */
        $event = Events::getDataFromRequest($data);
        //$event['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
        return Events::where('id', $id)->update($event);
    }

    /**
     * Update list event position
     * @param array $data
     * @return boolean
     */
    public function updatePosition($data)
    {
        /**
         * Process data before update
         */
        $total = Events::count();
        $newData = array_fill(1, $total, null);
        foreach($data as $e) {
            $position = ($e['position']);
            while ($newData[$position] != null) {
                if ($newData[$position]['id'] < $e['id']) {
                    $id = $newData[$position]['id'];
                    $newData[$position]['id'] = $e['id'];
                    $e['id'] = $id;
                }
                $position++;
            }
            $newData[$position] = [
                'id'=>$e['id'],
                'position' => $position
            ];
        }

        $idsChange = collect($data)->pluck('id');
        $eventPosition = Events::select('id', 'position')->whereNotIn('id', $idsChange)->orderBy('position', 'asc')->get();
        $idx = 1;
        foreach($eventPosition as $position) {
            while ($newData[$idx] != null) {
                $idx++;
            }
            $newData[$idx] = [
                'id'=>$position->id,
                'position' => $idx
            ];
            $idx++;
        }

        $updated = true;
        BaseModel::beginTransaction();
        try {
            for($i = 1; $i <= count($newData); $i++) {
                $element = $newData[$i];
                $element['updated_at'] = \Carbon\Carbon::now()->toDateTimeString();
                Events::where('id', $element['id'])->update($element);
            }
            BaseModel::commit();
        } catch (\Throwable $th) {
            $updated = false;
            BaseModel::rollBack();
        }

        return $updated;
    }

    /**
     * @param integer $id
     * @return boolean
     */
    public function delete($id)
    {
        $deleted = true;
        try {
            Events::where('id', (int)$id)->delete();
        } catch (\Throwable $th) {
            $deleted = false;
        }
        return $deleted;
    }

}
