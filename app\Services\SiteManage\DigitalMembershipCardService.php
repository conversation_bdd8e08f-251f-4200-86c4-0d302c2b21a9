<?php

namespace App\Services\SiteManage;

use App\Repositories\SiteManage\DigitalMembershipCardRepository;
use App\Services\S3Service;
use App\Shared\Constant;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Class DigitalMembershipCardService
 * 
 * This class provides functionality to manage digital membership cards.
 */
class DigitalMembershipCardService
{
    const MESSAGE_UPLOAD_FAILED = 'Failed to upload file to S3';

    private $digitalMembershipCardRepository;
    private $s3Service;

    /**
     * MESSAGE_SUCCESS
     */
    const MESSAGE_SUCCESS = 'Process is successful';
    /**
     * MESSAGE_FAILED
     */
    const MESSAGE_FAILED = 'Process is  failed';

    /**
     * DigitalMembershipCardService constructor.
     * 
     * @param DigitalMembershipCardRepository $digitalMembershipCardRepository The repository for digital membership cards.
     * @param S3Service $s3Service The service for interacting with Amazon S3.
     */
    public function __construct(DigitalMembershipCardRepository $digitalMembershipCardRepository, S3Service $s3Service)
    {
        $this->digitalMembershipCardRepository = $digitalMembershipCardRepository;
        $this->s3Service = $s3Service;
    }

    /**
     * Get a list of digital membership cards.
     *
     * @param array $param The parameters for filtering the digital membership cards.
     * @return \Illuminate\Database\Eloquent\Collection The list of digital membership cards.
     */
    public function getList(array $param)
    {
        Log::info('Start: DigitalMembershipCardService getList!');
        // Get all digital membership cards
        $digitalMembershipCards = $this->digitalMembershipCardRepository->getList($param);

        // Return the digital membership cards
        return $digitalMembershipCards;
    }

    /**
     * Create a new digital membership card.
     *
     * @param array $data The data for the digital membership card.
     * @return DigitalMembershipCard The created digital membership card.
     */
    public function create(array $data)
    {
        Log::info('Start: DigitalMembershipCardService create: ' . json_encode($data));
        // Validate the data if needed

        if (isset($data['image'])) {
            $data['image'] = $this->uploadFileToS3($data['image']);
            if ($data['image'] == '') {
                Log::error(self::MESSAGE_UPLOAD_FAILED);
                return false;
            }
        }

        // Create the digital membership card
        $data['position'] = 1;
        $this->digitalMembershipCardRepository->digitalMembershipCard->beginTransaction();
        try {
            $digitalMembershipCard = $this->digitalMembershipCardRepository->create($data);
            $cards = $this->digitalMembershipCardRepository->getCardsOrderByPosition();
            $position = 1;
            foreach ($cards as $card) {
                $card->position = $position;
                $card->save();
                $position++;
            }
            $this->digitalMembershipCardRepository->digitalMembershipCard->commit();

            // Return the created digital membership card
            return $digitalMembershipCard;
        } catch (\Exception $exception) {
            $this->digitalMembershipCardRepository->digitalMembershipCard->rollback();
            Log::info('DigitalMembershipCardService create exception: ' . $exception->getMessage());
            $this->s3Service->setPath($data['image'])->doDelete();
            return false;
        }
    }

    /**
     * Get a digital membership card by its ID.
     *
     * @param int $id The ID of the digital membership card.
     * @return DigitalMembershipCard|null The digital membership card, or null if not found.
     */
    public function get($id)
    {
        Log::info('Start: DigitalMembershipCardService get: ' . json_encode($id));
        // Get the digital membership card by its ID
        $digitalMembershipCard = $this->digitalMembershipCardRepository->getById($id);

        // Return the digital membership card
        return $digitalMembershipCard;
    }

    /**
     * Update a digital membership card.
     *
     * @param int $id The ID of the digital membership card.
     * @param array $data The updated data for the digital membership card.
     * @return DigitalMembershipCard|null The updated digital membership card, or null if not found.
     */
    public function update($id, array $data)
    {
        Log::info('Start: DigitalMembershipCardService update: ' . json_encode($data));
        // Validate the data if needed

        $record = $this->digitalMembershipCardRepository->getById($id);
        if (!$record) {
            return false;
        }

        if (!is_string($data['image'])) {
            $data['image'] = $this->uploadFileToS3($data['image']);
            if ($data['image'] == '') {
                Log::error(self::MESSAGE_UPLOAD_FAILED);
                return false;
            }
        }

        // Update the digital membership card
        $digitalMembershipCard = $this->digitalMembershipCardRepository->update($id, $data);

        if (!$digitalMembershipCard) {
            $this->s3Service->setPath($data['image'])->doDelete();
            return false;
        }

        if ($digitalMembershipCard && $data['image'] != $record->image) {
            $this->s3Service->setPath($record->image)->doDelete();
        }

        // Return the updated digital membership card
        return $digitalMembershipCard;
    }

    /**
     * Update the position of a digital membership card.
     *
     * @param array $param The parameters for updating the position of the digital membership card.
     * @return array The result of the update operation.
     */
    public function updatePosition($param)
    {

        $updated = $this->digitalMembershipCardRepository->updatePosition($param);

        return [
            'success' => $updated,
            'message' => $updated ? self::MESSAGE_SUCCESS : self::MESSAGE_FAILED
        ];
    }

    /**
     * Delete a digital membership card.
     *
     * @param int $id The ID of the digital membership card.
     * @return bool True if the digital membership card was deleted successfully, false otherwise.
     */
    public function delete($id)
    {
        Log::info('Start: DigitalMembershipCardService delete: ' . json_encode($id));

        // Delete the digital membership card
        $deleted = $this->digitalMembershipCardRepository->delete($id);

        // Return the result of the deletion
        return $deleted;
    }

    
    /**
     * Check if the digital membership card is enabled publicly.
     * 
     * @return bool True if the digital membership card is enabled publicly, false otherwise.
     */
    public function hasOnlyOneDefaultOn(array $data)
    {
        $count = $this->digitalMembershipCardRepository->hasOnlyOneDefaultOn($data);

        if ($count && $data['default'] === Constant::STATUS_SETTING['ON'] && $data['status'] === Constant::STATUS_DATA['PUBLIC']) {
            return true;
        }

        return false;
    }
    /**
     * Upload a file to Amazon S3.
     * 
     * @param string $file The file to upload.
     * @return string The URL of the uploaded file.
     */
    public function uploadFileToS3($file)
    {
        Log::info('Start: DigitalMembershipCardService uploadFileToS3');
        if ($file) {
            $uploaded = $this->s3Service->setPath('digital-membership-card')->setContent($file)->doUpload();
            if (!$uploaded) {
                Log::error(self::MESSAGE_UPLOAD_FAILED);
            } else {
                return $uploaded;
            }
        }
        return '';
    }
}
