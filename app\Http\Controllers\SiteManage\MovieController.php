<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\Medias;
use App\Services\SiteManage\MediaService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Medias Controller.
 *
 */
class MovieController extends Controller
{
    /**
     * @var Medias::class
     */
    protected $objectModel = Medias::class;
    /**
     * @var MediaService
     */
    private $mediaService;

    /**
     * @param MediaService $mediaService
     */
    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * Get list media.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'title' => request('titleFilter'),
                'status' => request('statusFilter'),
                'mediaType' => request('mediaTypeFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];
            return response()->json($this->mediaService->list($param));
        }

        return response()->json([], 403);
    }

    /**
     * Get media detail.
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            return response()->json($this->mediaService->detail($id));
        }
        return response()->json([], 403);
    }

    /**
     * Create a media.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            return response()->json($this->mediaService->create($request->all(), $request->file('file')));
        }
        return response()->json([], 403);
    }

    /**
     * Update a media.
     * @param Request $request
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }

            return response()->json($this->mediaService->update($request->all(), $request->file('file'), $id));
        }

        return response()->json([], 403);
    }

    /**
     * Update media position.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePosition(Request $request)
    {
        if ($this->hasPermission('update')) {
            return response()->json($this->mediaService->updatePosition($request->data));
        }

        return response()->json([], 403);
    }
    public function delete($id)
    {

        if ($this->hasPermission('delete')) {
            return response()->json($this->mediaService->delete($id));
        }

        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return string[]
     */
    private function arrayRules(Request $request)
    {
        // Check if status is draft
        $isDraft = $request->input('status') === 'draft';

        return [
            'title' => $isDraft ? 'max:100' : 'required|max:100',
            'status' => 'required',
            'media_type' => $isDraft ? '' : 'required',
            'start_at' => $isDraft ? 'nullable|date' : 'required|date',
            'end_at' => 'nullable|date|after:start_at',
            'file' => 'nullable|file|image',
            'vimeo_video_id' => $isDraft ? '' : 'required',
        ];
    }
}
