<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'storage/',
            'visibility' => 'public',
            'throw' => false,
        ],

        's3_fl' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_FL', ''),
            'secret' => env('AWS_SECRET_ACCESS_KEY_FL', ''),
            'region' => env('AWS_DEFAULT_REGION_FL', ''),
            'bucket' => env('AWS_BUCKET_FL', ''),
            'url' => env('AWS_URL_FL', ''),
            'endpoint' => env('AWS_ENDPOINT', ''),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT_FL', false),
            'throw' => false,
            'scheme'  => 'http'
        ],
        's3_ws' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_WS', ''),
            'secret' => env('AWS_SECRET_ACCESS_KEY_WS', ''),
            'region' => env('AWS_DEFAULT_REGION_WS', ''),
            'bucket' => env('AWS_BUCKET_WS', ''),
            'url' => env('AWS_URL_WS', ''),
            'endpoint' => env('AWS_ENDPOINT', ''),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT_WS', false),
            'throw' => false,
            'scheme'  => 'http'
        ],
        's3_kmn' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_KMN', ''),
            'secret' => env('AWS_SECRET_ACCESS_KEY_KMN', ''),
            'region' => env('AWS_DEFAULT_REGION_KMN', ''),
            'bucket' => env('AWS_BUCKET_KMN', ''),
            'url' => env('AWS_URL_KMN', ''),
            'endpoint' => env('AWS_ENDPOINT', ''),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT_KMN', false),
            'throw' => false,
            'scheme'  => 'http'
        ],
        's3_rl' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID_RL', ''),
            'secret' => env('AWS_SECRET_ACCESS_KEY_RL', ''),
            'region' => env('AWS_DEFAULT_REGION_RL', ''),
            'bucket' => env('AWS_BUCKET_RL', ''),
            'url' => env('AWS_URL_RL', ''),
            'endpoint' => env('AWS_ENDPOINT', ''),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT_RL', false),
            'throw' => false,
            'scheme'  => 'http'
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
