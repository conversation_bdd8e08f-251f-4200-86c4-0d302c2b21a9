<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RoleHasPermission extends Model
{
    use HasFactory;

    protected $table = 'role_has_permissions';

    /**
     * The attributes.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'permission_id',
        'role_id',
        'admin_id'
    ];

    public function permission() {
        return $this->belongsTo(AdminPermission::class, 'permission_id', 'id');
    }
}
