<?php

namespace App\Models\SiteManage;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class FaqsToFaqCategories extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'faq_id',
        'faq_category_id',
        'created_at',
        'updated_at'
    ];
    protected $table = 'faqs_to_faq_categories';
    
    public static function getDataFromRequest($data) {
        $dataRow = [];
        foreach(self::$fillable as $field){
            if(array_key_exists($field,$data)) {
                $dataRow[$field] = $data[$field];
            }
        }

        return $dataRow;
    }
}
