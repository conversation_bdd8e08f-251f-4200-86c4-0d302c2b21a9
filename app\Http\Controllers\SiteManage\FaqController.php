<?php

namespace App\Http\Controllers\SiteManage;

use App\Http\Controllers\Controller;
use App\Models\SiteManage\Faqs;
use App\Services\SiteManage\FaqService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Faqs Controller.
 *
 */
class FaqController extends Controller
{
    /**
     * @var Faqs::class
     */
    protected $objectModel = Faqs::class;
    /**
     * @var FaqService
     */
    private $faqService;

    /**
     * @param FaqService $faqService
     */
    public function __construct(FaqService $faqService)
    {
        $this->faqService = $faqService;
    }

    /**
     * Get list faq.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if ($this->hasPermission('viewAny')) {
            $param = [
                'id' => request('idFilter'),
                'title' => request('titleFilter'),
                'status' => request('statusFilter'),
                'size' => request('size'),
                'sort' => request('sort'),
            ];
            return response()->json($this->faqService->list($param));
        }

        return response()->json([], 403);
    }

    /**
     * Get faq detail.
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail($id)
    {
        if ($this->hasPermission('view')) {
            return response()->json($this->faqService->detail($id));
        }

        return response()->json([], 403);
    }

    /**
     * Create faq.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        if ($this->hasPermission('create')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }
            return response()->json($this->faqService->create($request->all()));
        }

        return response()->json([], 403);
    }

    /**
     * Update faq.
     * @param Request $request
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->hasPermission('update')) {
            $validator = Validator::make($request->all(), $this->arrayRules($request));
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ]);
            }

            $param = $request->all();

            return response()->json($this->faqService->update($param, $id));
        }

        return response()->json([], 403);
    }

    /**
     * Update faq position.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePosition(Request $request)
    {
        if ($this->hasPermission('update')) {
            return response()->json($this->faqService->updatePosition($request->data));
        }

        return response()->json([], 403);
    }

    /**
     * Delete faq.
     * @param integer $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        if ($this->hasPermission('delete')) {
            return response()->json($this->faqService->delete($id));
        }

        return response()->json([], 403);
    }

    /**
     * @param Request $request
     * @return array
     */
    private function arrayRules(Request $request)
    {
        return [
            'title' => 'required|max:100',
            'status' => 'required',
            'categories' => 'required',
            'description' => 'required',
        ];
    }
}
